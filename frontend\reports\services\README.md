# Reports Page Services

This directory contains services used by the SmartTest Reports page to fetch and process data from various sources.

## Services

### External API Service

The `ExternalApiService` provides direct integration with the external API endpoints on port 9080 for retrieving test reports and details using cookie-based authentication.

**Key Features:**
- Cookie-based authentication with JSESSIONID
- HTML parsing to extract data from responses
- Automatic session renewal
- Error handling and fallbacks

**Usage:**
```javascript
// Get report summary
const reportSummary = await window.externalApiService.getReportSummary(
    tsnId,
    uid,
    password
);

// Get report details
const reportDetails = await window.externalApiService.getReportDetails(
    tsnId,
    1, // Page number
    uid,
    password
);

// Get multiple reports
const reports = await window.externalApiService.getRecentTestRuns(
    sessionIds,
    uid,
    password,
    limit
);
```

### Session ID Service

The `SessionIdService` provides methods to get recent test session IDs from various sources:
1. Local storage cache
2. Database API (when available)
3. Hardcoded fallback values for testing

**Key Features:**
- Multi-source data retrieval
- Local storage caching
- Automatic fallback to alternative sources

**Usage:**
```javascript
// Get recent session IDs
const sessionIds = await window.sessionIdService.getRecentSessionIds(
    credentials,
    limit
);
```

## Architecture

These services implement a hybrid data access approach that combines direct external API integration with database access. This provides several benefits:

1. **Performance**: Direct API calls are faster than going through the database layer
2. **Reliability**: Reports page works even if the database is unavailable
3. **Flexibility**: Complex analytics can still use the database when needed

For more details on the hybrid approach, see the [Hybrid Data Access documentation](../../server/documentation/Integration/hybrid-data-access.md).

## Development

### Adding a New Service

To add a new service:

1. Create a new JavaScript file in this directory
2. Implement the service as a class
3. Create a global instance at the end of the file
4. Add a script tag to `index.html`

### Testing

Services can be tested in the browser console:

```javascript
// Test external API service
const testReport = await window.externalApiService.getReportSummary(
    '13782',
    '<EMAIL>',
    'test'
);
console.log(testReport);

// Test session ID service
const sessionIds = await window.sessionIdService.getRecentSessionIds(
    { uid: '<EMAIL>', password: 'test' },
    5
);
console.log(sessionIds);
```
