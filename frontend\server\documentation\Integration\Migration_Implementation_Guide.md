# Migration Implementation Guide

## Phase 1: Foundation Implementation

This guide provides step-by-step instructions for implementing Phase 1 of the unified architecture migration.

### Step 1.1: Create Shared Directory Structure

```bash
# Create the shared directory structure
mkdir -p frontend/shared/services
mkdir -p frontend/shared/utils
mkdir -p frontend/shared/store
mkdir -p frontend/shared/components
mkdir -p tests/unit/services
mkdir -p tests/integration
mkdir -p tests/e2e
mkdir -p tests/utils
```

### Step 1.2: Create Unified Configuration

**File:** `frontend/shared/utils/config.js`

```javascript
/**
 * Unified Configuration for SmartTest Application
 * Centralizes all configuration from dashboard, reports, and config modules
 */
export class AppConfig {
  constructor() {
    this.environment = this.detectEnvironment();
    this.initializeEndpoints();
    this.initializeDefaults();
  }

  detectEnvironment() {
    if (typeof window !== 'undefined') {
      const hostname = window.location.hostname;
      return (hostname === 'localhost' || hostname === '127.0.0.1') ? 'development' : 'production';
    }
    return 'development';
  }

  initializeEndpoints() {
    this.endpoints = {
      // Local API endpoints (database)
      local: {
        testSuites: '/local/test-suites',
        testCases: '/local/test-cases', 
        activeTests: '/local/active-tests',
        recentRuns: '/local/recent-runs',
        testDetails: '/local/test-details'
      },
      
      // API endpoints (business logic)
      api: {
        caseRunner: '/api/case-runner',
        runSuite: '/api/run-suite',
        testStatus: '/api/test-status',
        testReport: '/api/test-report',
        stopTest: '/api/stop-test',
        rerunFailed: '/api/rerun-failed',
        testReports: '/api/test-reports'
      },
      
      // External API endpoints (port 9080)
      external: {
        baseUrl: 'http://mprts-qa02.lab.wagerworks.com:9080',
        login: '/AutoRun/Login',
        caseRunner: '/AutoRun/CaseRunner',
        reportSummary: '/AutoRun/ReportSummary',
        reportDetails: '/AutoRun/ReportDetails',
        removeSession: '/AutoRun/RemoveSession'
      }
    };
  }

  initializeDefaults() {
    // Default test parameters (from existing services)
    this.defaultTestParams = {
      environment: 'qa02',
      shell_host: 'jps-qa10-app01',
      file_path: '/home/<USER>/',
      operatorConfigs: 'operatorNameConfigs',
      kafka_server: 'kafka-qa-a0.lab.wagerworks.com',
      dataCenter: 'GU',
      rgs_env: 'qa02',
      old_version: '0',
      networkType1: 'multi-site',
      networkType2: 'multi-site',
      sign: '-',
      rate_src: 'local'
    };

    // Request configuration
    this.requestConfig = {
      timeout: 30000,
      retries: 3,
      retryDelay: 1000
    };

    // Reports configuration (from reports module)
    this.reportsConfig = {
      reportingEndpoint: '/local/recent-runs',
      testDetailsEndpoint: '/local/test-details',
      refreshInterval: 30000,
      useDirectExternalApi: false,
      externalApiBaseUrl: '/api',
      maxReportsToShow: 25,
      autoRefresh: false
    };
  }

  getEndpoint(category, endpoint) {
    return this.endpoints[category]?.[endpoint] || endpoint;
  }

  getDefaultTestParams() {
    return { ...this.defaultTestParams };
  }

  getRequestConfig() {
    return { ...this.requestConfig };
  }

  getReportsConfig() {
    return { ...this.reportsConfig };
  }
}

// Create singleton instance
export const appConfig = new AppConfig();
```

### Step 1.3: Create Authentication Store

**File:** `frontend/shared/store/auth-store.js`

```javascript
/**
 * Unified Authentication Store
 * Manages authentication state across all modules
 */
export class AuthStore {
  constructor() {
    this.credentials = { uid: '', password: '' };
    this.isAuthenticated = false;
    this.listeners = [];
    this.loadCredentials();
  }

  /**
   * Set user credentials
   */
  setCredentials(username, password) {
    this.credentials = { uid: username, password: password };
    this.isAuthenticated = true;
    
    // Save to session storage
    try {
      sessionStorage.setItem('smarttest_uid', username);
      sessionStorage.setItem('smarttest_pwd', password);
    } catch (error) {
      console.warn('Could not save credentials to session storage:', error);
    }
    
    this.notifyListeners();
    console.log('Credentials set for user:', username);
    return true;
  }

  /**
   * Load credentials from session storage
   */
  loadCredentials() {
    try {
      const uid = sessionStorage.getItem('smarttest_uid');
      const password = sessionStorage.getItem('smarttest_pwd');

      if (uid && password) {
        this.credentials = { uid, password };
        this.isAuthenticated = true;
        console.log(`Credentials loaded for user: ${uid}`);
        return true;
      }

      console.log('No valid credentials found');
      return false;
    } catch (error) {
      console.error('Error loading credentials:', error);
      this.credentials = { uid: '', password: '' };
      this.isAuthenticated = false;
      return false;
    }
  }

  /**
   * Clear credentials and logout
   */
  logout() {
    this.credentials = { uid: '', password: '' };
    this.isAuthenticated = false;
    
    try {
      sessionStorage.removeItem('smarttest_uid');
      sessionStorage.removeItem('smarttest_pwd');
    } catch (error) {
      console.warn('Could not clear session storage:', error);
    }
    
    this.notifyListeners();
    console.log('User logged out');
  }

  /**
   * Get current credentials
   */
  getCredentials() {
    return { ...this.credentials };
  }

  /**
   * Check if user is authenticated
   */
  isUserAuthenticated() {
    return this.isAuthenticated && this.credentials.uid && this.credentials.password;
  }

  /**
   * Subscribe to authentication changes
   */
  subscribe(listener) {
    this.listeners.push(listener);
    return () => {
      this.listeners = this.listeners.filter(l => l !== listener);
    };
  }

  /**
   * Notify all listeners of authentication changes
   */
  notifyListeners() {
    this.listeners.forEach(listener => {
      try {
        listener(this.isAuthenticated, this.credentials);
      } catch (error) {
        console.error('Error in auth listener:', error);
      }
    });
  }
}

// Create singleton instance
export const authStore = new AuthStore();
```

## Next Steps

After completing Phase 1 foundation:

1. **Test the foundation:** Verify all shared services work independently
2. **Create unified API service:** Combine functionality from all three existing services
3. **Create external API service:** Extract and centralize external API logic
4. **Begin module migration:** Start with dashboard module

The foundation provides:
- ✅ Centralized configuration management
- ✅ Unified authentication state
- ✅ Common API service functionality
- ✅ Proper error handling and retry logic
- ✅ Backward compatibility with existing code

This foundation ensures that the migration preserves all existing functionality while providing a solid base for the unified architecture.
