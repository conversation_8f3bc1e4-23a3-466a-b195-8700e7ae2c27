/**
 * SmartTest API Service
 *
 * This service provides the real API integration for the SmartTest application,
 * interacting with the backend services to run and monitor tests.
 */

class ApiService {
  constructor() {
    // For development, use a hardcoded URL to connect to the actual server
    // In production, this would be configurable or use the current origin
    this.baseUrl = 'http://localhost:3000';

    // Log the base URL for debugging
    console.log('API Service initialized with baseUrl:', this.baseUrl);

    // Setup API endpoints
    this.endpoints = {
      caseRunner: '/api/case-runner',         // -> /api/case-runner
      testStatus: '/api/test-status',         // -> /api/test-status
      testReport: '/api/test-report',         // -> /api/test-report
      testSuites: '/local/test-suites',       // -> /local/test-suites
      testCases: '/local/test-cases',         // -> /local/test-cases
      stopTest: '/api/stop-test',             // -> /api/stop-test
      rerunFailed: '/api/rerun-failed',       // -> /api/rerun-failed
      activeTests: '/local/active-tests',     // -> /local/active-tests
      recentRuns: '/local/recent-runs',       // -> /local/recent-runs
      testDetailsEndpoint: '/local/test-details', // -> /local/test-details/:tsn_id
      testReports: '/api/test-reports'        // -> /api/test-reports
    };

    // Default credentials
    this.credentials = { uid: '', password: '' };

    // Load credentials from session storage
    this.loadCredentials();

    // Debug log the endpoints
    console.log('API endpoints initialized:', this.endpoints);
  }

  /**
   * Set API credentials
   * @param {string} username - Username
   * @param {string} password - Password
   */
  setCredentials(username, password) {
    this.credentials = { uid: username, password: password };

    // Save to session storage
    sessionStorage.setItem('smarttest_uid', username);
    sessionStorage.setItem('smarttest_pwd', password);

    console.log('API credentials set for user:', username);
    return true;
  }

  /**
   * Load credentials from session storage or environment
   * @returns {boolean} - Whether credentials were successfully loaded
   */
  loadCredentials() {
    try {
      // Try to load from session storage first
      const uid = sessionStorage.getItem('smarttest_uid');
      const password = sessionStorage.getItem('smarttest_pwd');

      if (uid && password) {
        this.credentials = { uid, password };
        console.log(`Credentials loaded from session storage for user: ${uid}`);
        return true;
      } else {
        console.warn('No valid credentials found in session storage. User might need to log in or defaults will be used.');
        // Ensure credentials are reset to default if not found, to avoid using stale ones from a previous context
        this.credentials = { uid: '', password: '' }; 
        return false;
      }
    } catch (error) {
      console.error('Error accessing session storage in loadCredentials():', error.message);
      console.warn('Session storage may not be accessible in this context (e.g., private browsing, sandboxed iframe). Using default/empty credentials.');
      // Fallback to default/empty credentials if storage access fails
      this.credentials = { uid: '', password: '' }; 
      return false;
    }
  }

  /**
   * Get authentication parameters for API requests
   * @returns {Object} - Authentication parameters
   */
  getAuthParams() {
    return {
      uid: this.credentials.uid,
      password: this.credentials.password
    };
  }

  /**
   * Make a GET request to the API
   * @param {string} endpoint - The endpoint to call
   * @param {Object} params - Additional parameters
   * @returns {Promise<any>} - The response from the API
   */
  async getRequest(endpoint, params = {}) {
    try {
      // Determine if endpoint is local (should not prepend baseUrl)
      let url;
      if (endpoint.startsWith('/local/')) {
        url = endpoint; // Call local endpoints directly
      } else {
        url = this.baseUrl + endpoint;
      }

      console.log(`Making GET request to: ${url}`);

      // Add authentication parameters and any additional parameters
      const allParams = {
        ...params,
        uid: this.credentials.uid,
        password: this.credentials.password
      };

      // Build query string
      const queryString = Object.entries(allParams)
        .map(([key, value]) => `${encodeURIComponent(key)}=${encodeURIComponent(value)}`)
        .join('&');

      // Make the request
      const response = await fetch(`${url}?${queryString}`);

      // Handle non-200 responses
      if (!response.ok) {
        const errorText = await response.text();
        console.error(`API error (${response.status}): ${errorText}`);
        throw new Error(`API request failed with status ${response.status}`);
      }

      // Parse the response
      const data = await response.json();

      return data;
    } catch (error) {
      console.error(`Error making GET request to ${endpoint}:`, error);
      throw error;
    }
  }

  /**
   * Make a POST request to the API
   * @param {string} endpoint - The endpoint to call
   * @param {Object} params - The parameters to send
   * @returns {Promise<any>} - The response from the API
   */
  async postRequest(endpoint, params = {}) {
    try {
      // Build complete URL
      const url = this.baseUrl + endpoint;

      console.log(`Making POST request to: ${url}`);

      // Add authentication parameters
      const allParams = {
        ...params,
        ...this.getAuthParams()
      };

      // Make the request
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(allParams)
      });

      // Handle non-200 responses
      if (!response.ok) {
        const errorText = await response.text();
        console.error(`API error (${response.status}): ${errorText}`);
        throw new Error(`API error (${response.status}): ${errorText}`);
      }

      // Parse JSON response
      const data = await response.json();
      return data;
    } catch (error) {
      console.error(`Error making POST request to ${endpoint}:`, error);
      throw error;
    }
  }

  /**
   * Run a specific test case by ID
   * @param {string} tcId - Test case ID to run
   * @param {Object} params - Additional parameters to pass to the test case runner
   * @returns {Promise<Object>} - Response from the API
   */
  async runTestCase(tcId, params = {}) {
    try {
      const response = await this.postRequest(this.endpoints.caseRunner, {
        tc_id: tcId,
        ...ApiService.DEFAULT_TEST_PARAMS,
        ...params
      });

      if (response && response.tsn_id) {
        return response;
      } else {
        throw new Error('Failed to get test suite run ID');
      }
    } catch (error) {
      console.error(`Error running test case ${tcId}:`, error);
      throw error;
    }
  }

  /**
   * Run a test suite (sequential, single tsn_id)
   * @param {number} tsId - Test suite ID
   * @param {Object} params - Additional parameters
   * @returns {Promise<string>} - Test suite run/session ID (tsn_id)
   */
  async runTestSuite(tsId, params = {}) {
    try {
      const response = await this.postRequest(this.endpoints.caseRunner, {
        ts_id: tsId,
        ...ApiService.DEFAULT_TEST_PARAMS,
        ...params
      });

      if (response && response.tsn_id) {
        return response.tsn_id;
      } else {
        throw new Error('Failed to get test suite run ID');
      }
    } catch (error) {
      console.error(`Error running test suite ${tsId}:`, error);
      throw error;
    }
  }

  /**
   * Get test status
   * @param {number} tsnId - Test suite run ID
   * @returns {Promise<Object>} - Test status data
   */
  async getTestStatus(tsnId) {
    try {
      return await this.getRequest(this.endpoints.testStatus, { tsn_id: tsnId });
    } catch (error) {
      console.error(`Error getting test status for ${tsnId}:`, error);
      throw error;
    }
  }

  /**
   * Get report summary for a test run
   * @param {number} tsnId - Test suite run ID
   * @returns {Promise<Object>} - Test report data
   */
  async getReportSummary(tsnId) {
    try {
      return await this.getRequest(this.endpoints.testReport, { tsn_id: tsnId });
    } catch (error) {
      console.error(`Error getting report summary for ${tsnId}:`, error);
      throw error;
    }
  }

  /**
   * Get available test suites
   * @returns {Promise<Array>} - List of test suites
   */
  async getTestSuites() {
    try {
      const response = await this.getRequest(this.endpoints.testSuites);
      return response.testSuites || [];
    } catch (error) {
      console.error('Error getting test suites:', error);
      throw error;
    }
  }

  /**
   * Get available test cases
   * @returns {Promise<Array>} - List of test cases
   */
  async getTestCases() {
    try {
      const response = await this.getRequest(this.endpoints.testCases);
      return response.testCases || [];
    } catch (error) {
      console.error('Error getting test cases:', error);
      throw error;
    }
  }

  /**
   * Stop a running test
   * @param {number} tsnId - Test suite run ID
   * @returns {Promise<boolean>} - Success status
   */
  async stopTest(tsnId) {
    try {
      const response = await this.postRequest(this.endpoints.stopTest, { tsn_id: tsnId });
      return response.success === true;
    } catch (error) {
      console.error(`Error stopping test ${tsnId}:`, error);
      throw error;
    }
  }

  /**
   * Rerun failed tests from a previous test run
   * @param {number} tsnId - Original test suite run ID
   * @param {Object} params - Additional parameters
   * @returns {Promise<number>} - New test suite run ID
   */
  async rerunFailedTests(tsnId, params = {}) {
    try {
      const response = await this.postRequest(this.endpoints.rerunFailed, {
        tsn_id: tsnId,
        user_id: this.credentials.uid,
        username: this.credentials.uid,
        ...params
      });

      if (response && response.tsn_id) {
        return response.tsn_id;
      } else {
        throw new Error('Failed to get test suite run ID for rerun');
      }
    } catch (error) {
      console.error(`Error rerunning failed tests from ${tsnId}:`, error);
      throw error;
    }
  }

  /**
   * Get active tests
   * @returns {Promise<Array>} - List of active tests
   */
  async getActiveTests() {
    try {
      const response = await this.getRequest(this.endpoints.activeTests);
      return response.activeTests || [];
    } catch (error) {
      console.error('Error getting active tests:', error);
      throw error;
    }
  }

  /**
   * Get test results data for dashboard display
   * @returns {Promise<Object>} - Dashboard data
   */
  async getDashboardData() {
    try {
      // Query for active test data
      const activeTestsData = await this.getActiveTests();

      // Get recent test runs - we'll get this from SQL in real implementation
      // For now, construct from active tests data
      const recentRuns = activeTestsData.map(test => ({
        id: test.tsn_id,
        type: 'Test Case',
        environment: 'QA02',
        status: test.status || 'running',
        startTime: test.latest_activity || new Date().toISOString(),
        duration: 0
      }));

      // Count status totals
      const total = activeTestsData.length;
      const successful = activeTestsData.filter(test => test.outcome === 'P').length;
      const failed = activeTestsData.filter(test => test.outcome === 'F').length;
      const running = activeTestsData.filter(test => !test.outcome).length;

      return {
        summary: { total, successful, failed, running },
        recent: recentRuns,
        environment: 'QA02'
      };
    } catch (error) {
      console.error('Error getting dashboard data:', error);
      throw error;
    }
  }

  /**
   * Get test reports for the reports page
   * @param {Object} params - Optional parameters like timeRange
   * @returns {Promise<Array>} - List of test reports
   */
  async getTestReports(params = {}) {
    try {
      // Params can include: time_range, since_tsn_id.
      // The backend will determine if it's a full fetch or incremental based on since_tsn_id.
      // No client-side pagination `limit` is sent for these types of requests by default in this new flow.
      const responseData = await this.getRequest(this.endpoints.testReports, params);
      
      // The responseData is expected to be the full object from the server, e.g.:
      // { success: true, reports: [...], totalRecords: N, highestTsnIdInResponse: M } OR
      // { success: true, newRuns: [...], totalRecords: N, latestTsnIdInDelta: M }
      // It also includes the `success` flag from the server route.
      return responseData; 
    } catch (error) {
      console.error('Error getting test reports from ApiService:', error);
      // Return a structured error to be handled by the caller in reports.js
      return { success: false, message: error.message || 'Network or API error in ApiService' };
    }
  }

  /**
   * Get recent test runs
   * @param {Object} options - Query options (limit, timeRange, etc.)
   * @returns {Promise<Object>} - API response with recent runs data
   */
  async getRecentRuns(options = {}) {
    try {
      const response = await this.getRequest(this.endpoints.recentRuns, options);
      // Handle both response formats for backward compatibility
      if (response.success && Array.isArray(response.data)) {
        return response.data;
      } else if (Array.isArray(response)) {
        return response;
      }
      return [];
    } catch (error) {
      console.error('Error getting recent runs:', error);
      throw error;
    }
  }

  /**
   * Get test details
   * @param {string|number} tsnId - Test session ID
   * @returns {Promise<Object>} - API response with test details
   */
  async getTestDetails(tsnId) {
    try {
      console.log(`Getting test details for ${tsnId} using endpoint ${this.endpoints.testDetailsEndpoint}`);

      const response = await this.getRequest(`${this.endpoints.testDetailsEndpoint}/${tsnId}`);

      // Handle different response formats for backward compatibility
      if (response.success && response.test) {
        // Format from /local/test-details/:tsn_id endpoint
        return response.test;
      } else if (response.success && response.data) {
        // Generic success response format
        return response.data;
      } else if (response.test) {
        // Direct test object
        return response.test;
      }

      // Fallback to returning the whole response
      return response;
    } catch (error) {
      console.error(`Error getting test details for ${tsnId}:`, error);
      throw error;
    }
  }

  // Default test parameters to use with API requests
  static get DEFAULT_TEST_PARAMS() {
    return {
      environment: 'qa02',
      shell_host: 'jps-qa10-app01',
      file_path: '/home/<USER>/',
      operatorConfigs: 'operatorNameConfigs',
      kafka_server: 'kafka-qa-a0.lab.wagerworks.com',
      dataCenter: 'GU',
      rgs_env: 'qa02',
      old_version: '0',
      networkType1: 'multi-site',
      networkType2: 'multi-site',
      sign: '-',
      rate_src: 'local'
    };
  }
}

// Create global API service instance
window.apiService = new ApiService();

// Export for module usage (if needed)
if (typeof module !== 'undefined' && typeof module.exports !== 'undefined') {
  module.exports = ApiService;
}
