/**
 * SmartTest API Service
 *
 * This service provides the real API integration for the SmartTest application,
 * interacting with the backend services to run and monitor tests.
 */

class ApiService {
  constructor() {
    // Use relative URL that will resolve correctly with the new server architecture
    // This works because all requests will be served from the same origin
    this.baseUrl = '/api';

    // Log the base URL for debugging
    console.log('API Service initialized with baseUrl:', this.baseUrl);

    // Setup API endpoints
    this.endpoints = {
      caseRunner: '/case-runner',         // -> /api/case-runner
      testStatus: '/test-status',         // -> /api/test-status
      testReport: '/test-report',         // -> /api/test-report
      testSuites: '/local/test-suites',   // -> /local/test-suites
      testCases: '/local/test-cases',     // -> /local/test-cases
      stopTest: '/stop-test',             // -> /api/stop-test
      rerunFailed: '/rerun-failed',       // -> /api/rerun-failed
      activeTests: '/local/active-tests', // -> /local/active-tests
      recentRuns: '/local/recent-runs',   // -> /local/recent-runs
      testReports: '/test-reports'        // -> /api/test-reports
    };

    // Default credentials
    this.credentials = { uid: '', password: '' };

    // Load credentials from session storage
    this.loadCredentials();

    // Debug log the endpoints
    console.log('API endpoints initialized:', this.endpoints);
  }

  /**
   * Set API credentials
   * @param {string} username - Username
   * @param {string} password - Password
   */
  setCredentials(username, password) {
    this.credentials = { uid: username, password: password };

    // Save to session storage
    sessionStorage.setItem('smarttest_uid', username);
    sessionStorage.setItem('smarttest_pwd', password);

    console.log('API credentials set for user:', username);
    return true;
  }

  /**
   * Load credentials from session storage or environment
   * @returns {boolean} - Whether credentials were successfully loaded
   */
  loadCredentials() {
    try {
      // Try to load from session storage first
      const uid = sessionStorage.getItem('smarttest_uid');
      const password = sessionStorage.getItem('smarttest_pwd');

      if (uid && password) {
        this.credentials = { uid, password };
        console.log(`Credentials loaded for user: ${uid}`);
        return true;
      }

      console.log('No valid credentials found, user needs to log in');
      return false;
    } catch (error) {
      console.error('Error loading credentials:', error);
      return false;
    }
  }

  /**
   * Get authentication parameters for API requests
   * @returns {Object} - Authentication parameters
   */
  getAuthParams() {
    return {
      uid: this.credentials.uid,
      password: this.credentials.password
    };
  }

  /**
   * Make a GET request to the API
   * @param {string} endpoint - The endpoint to call
   * @param {Object} params - Additional parameters
   * @returns {Promise<any>} - The response from the API
   */
  async getRequest(endpoint, params = {}) {
    let url;
    try {
      if (endpoint.startsWith('/local/')) {
        url = endpoint; // No /api prefix for local endpoints
      } else {
    url = this.baseUrl + endpoint;
  }

      console.log(`Making GET request to: ${url}`);

      // Add authentication parameters and any additional parameters
      const allParams = {
        ...params,
        uid: this.credentials.uid,
        password: this.credentials.password
      };

      // Build query string
      const queryString = Object.entries(allParams)
        .map(([key, value]) => `${encodeURIComponent(key)}=${encodeURIComponent(value)}`)
        .join('&');

      // Make the request
      const response = await fetch(`${url}?${queryString}`);

      // Handle non-200 responses
      if (!response.ok) {
        const errorText = await response.text();
        console.error(`API error (${response.status}): ${errorText}`);
        throw new Error(`API request failed with status ${response.status}`);
      }

      // Parse the response
      const data = await response.json();

      return data;
    } catch (error) {
      console.error(`Error making GET request to ${endpoint}:`, error);
      throw error;
    }
  }

  /**
   * Make a POST request to the API
   * @param {string} endpoint - The endpoint to call
   * @param {Object} params - The parameters to send
   * @returns {Promise<any>} - The response from the API
   */
  async postRequest(endpoint, params = {}) {
    try {
      // Build full URL with the correct server address
      const url = this.baseUrl + endpoint;

      console.log(`Making POST request to: ${url}`);

      // Add authentication parameters directly
      const requestData = {
        ...params,
        uid: this.credentials.uid,
        password: this.credentials.password
      };

      // Use URLSearchParams for proper form encoding
      const formData = new URLSearchParams();

      // Add all parameters to form data
      Object.entries(requestData).forEach(([key, value]) => {
        formData.append(key, value);
      });

      // Log request parameters for debugging (mask password)
      const logParams = {...requestData};
      if (logParams.password) logParams.password = '***';
      console.log('Request parameters:', logParams);

      // Make the request
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        },
        body: formData
      });

      // Handle non-200 responses
      if (!response.ok) {
        const errorText = await response.text();
        console.error(`API error (${response.status}): ${errorText}`);
        throw new Error(`API request failed with status ${response.status}`);
      }

      // Parse the response
      const data = await response.json();

      // Add success property for compatibility with frontend
      return { success: true, ...data };
    } catch (error) {
      console.error(`Error making POST request to ${endpoint}:`, error);
      throw error;
    }
  }

  /**
   * Run a specific test case by ID
   * @param {string} tcId - Test case ID to run
   * @param {Object} params - Additional parameters to pass to the test case runner
   * @returns {Promise<Object>} - Response from the API
   */
  async runTestCase(tcId, params = {}) {
    console.log(`Running test case ${tcId} with params:`, params);
    console.log('Using endpoint:', this.endpoints.caseRunner);

    // Ensure we have the minimal required parameters
    const runParams = {
      tc_id: tcId,
      envir: params.envir || 'qa02',
      shell_host: params.shell_host || 'jps-qa10-app01',
      ...params // Any other params provided by the caller
    };

    try {
      // Call the CaseRunner API via our backend proxy
      const response = await this.postRequest(this.endpoints.caseRunner, runParams);
      console.log(`Test case ${tcId} run initiated:`, response);
      return response;
    } catch (error) {
      console.error(`Error running test case ${tcId}:`, error);
      throw error;
    }
  }

  /**
   * Run a test suite (sequential, single tsn_id)
   * @param {number} tsId - Test suite ID
   * @param {Object} params - Additional parameters
   * @returns {Promise<string>} - Test suite run/session ID (tsn_id)
   */
  async runTestSuite(tsId, params = {}) {
    try {
      if (!tsId) {
        throw new Error('Test suite ID is required');
      }
      // Add required parameters for the test suite runner
      const testParams = {
        ts_id: tsId,
        user_id: this.credentials.uid,
        username: this.credentials.uid,
        ...ApiService.DEFAULT_TEST_PARAMS,
        ...params
      };
      // Use the new /api/run-suite endpoint for sequential suite runs
      const response = await this.postRequest('/run-suite', testParams);
      if (response && response.tsn_id) {
        console.log(`Test suite ${tsId} running with session ID: ${response.tsn_id}`);
        return response.tsn_id;
      } else {
        throw new Error(response.message || `Failed to run test suite ${tsId}`);
      }
    } catch (error) {
      console.error(`Error running test suite ${tsId}:`, error);
      throw error;
    }
  }

  /**
   * Get test status
   * @param {number} tsnId - Test suite run ID
   * @returns {Promise<Object>} - Test status data
   */
  async getTestStatus(tsnId) {
    try {
      return await this.getRequest(this.endpoints.testStatus, { tsn_id: tsnId });
    } catch (error) {
      console.error(`Error getting test status for ${tsnId}:`, error);
      throw error;
    }
  }

  /**
   * Get report summary for a test run
   * @param {number} tsnId - Test suite run ID
   * @returns {Promise<Object>} - Test report data
   */
  async getReportSummary(tsnId) {
    try {
      return await this.getRequest(this.endpoints.testReport, { tsn_id: tsnId });
    } catch (error) {
      console.error(`Error getting report for ${tsnId}:`, error);
      throw error;
    }
  }

  /**
   * Get available test suites
   * @returns {Promise<Array>} - List of test suites
   */
  async getTestSuites() {
    try {
      const response = await this.getRequest(this.endpoints.testSuites);
      return response.testSuites || [];
    } catch (error) {
      console.error('Error getting test suites:', error);
      throw error;
    }
  }

  /**
   * Get available test cases
   * @returns {Promise<Array>} - List of test cases
   */
  async getTestCases() {
    try {
      const response = await this.getRequest(this.endpoints.testCases);
      return response;
    } catch (error) {
      console.error('Error getting test cases:', error);
      throw error;
    }
  }

  /**
   * Stop a running test
   * @param {number} tsnId - Test suite run ID
   * @returns {Promise<boolean>} - Success status
   */
  async stopTest(tsnId) {
    try {
      const response = await this.postRequest(this.endpoints.stopTest, { tsn_id: tsnId });
      return response.success === true;
    } catch (error) {
      console.error(`Error stopping test ${tsnId}:`, error);
      throw error;
    }
  }

  /**
   * Rerun failed tests from a previous test run
   * @param {number} tsnId - Original test suite run ID
   * @param {Object} params - Additional parameters
   * @returns {Promise<number>} - New test suite run ID
   */
  async rerunFailedTests(tsnId, params = {}) {
    try {
      const response = await this.postRequest(this.endpoints.rerunFailed, {
        tsn_id: tsnId,
        user_id: this.credentials.uid,
        username: this.credentials.uid,
        ...params
      });

      if (response && response.tsn_id) {
        return response.tsn_id;
      } else {
        throw new Error('Failed to get test suite run ID for rerun');
      }
    } catch (error) {
      console.error(`Error rerunning failed tests from ${tsnId}:`, error);
      throw error;
    }
  }

  /**
   * Get active tests
   * @returns {Promise<Array>} - List of active tests
   */
  async getActiveTests() {
    try {
      const response = await this.getRequest(this.endpoints.activeTests);
      return response.activeTests || [];
    } catch (error) {
      console.error('Error getting active tests:', error);
      throw error;
    }
  }

  /**
   * Get test results data for dashboard display
   * @returns {Promise<Object>} - Dashboard data
   */
  async getDashboardData() {
    try {
      // Query for active test data
      const activeTestsData = await this.getActiveTests();

      // Get recent test runs - we'll get this from SQL in real implementation
      // For now, construct from active tests data
      const recentRuns = activeTestsData.map(test => ({
        id: test.tsn_id,
        type: 'Test Case',
        environment: 'QA02',
        status: test.status || 'running',
        startTime: test.latest_activity || new Date().toISOString(),
        duration: 0
      }));

      // Count status totals
      const total = activeTestsData.length;
      const successful = activeTestsData.filter(test => test.outcome === 'P').length;
      const failed = activeTestsData.filter(test => test.outcome === 'F').length;
      const running = activeTestsData.filter(test => !test.outcome).length;

      return {
        summary: { total, successful, failed, running },
        recent: recentRuns,
        environment: 'QA02'
      };
    } catch (error) {
      console.error('Error getting dashboard data:', error);
      throw error;
    }
  }

  /**
   * Get test reports for the reports page
   * @param {Object} params - Optional parameters like timeRange
   * @returns {Promise<Array>} - List of test reports
   */
  async getTestReports(params = {}) {
    try {
      const response = await this.getRequest(this.endpoints.testReports, params);
      return response.reports || [];
    } catch (error) {
      console.error('Error getting test reports:', error);
      throw error;
    }
  }

  // Default test parameters to use with API requests
  static get DEFAULT_TEST_PARAMS() {
    return {
      environment: 'qa02',
      shell_host: 'jps-qa10-app01',
      file_path: '/home/<USER>/',
      operatorConfigs: 'operatorNameConfigs',
      kafka_server: 'kafka-qa-a0.lab.wagerworks.com',
      dataCenter: 'GU',
      rgs_env: 'qa02',
      old_version: '0',
      networkType1: 'multi-site',
      networkType2: 'multi-site',
      sign: '-',
      rate_src: 'local'
    };
  }
}

// Create global API service instance
window.apiService = new ApiService();

// Export for module usage (if needed)
if (typeof module !== 'undefined' && typeof module.exports !== 'undefined') {
  module.exports = ApiService;
}