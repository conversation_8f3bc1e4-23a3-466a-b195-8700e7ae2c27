/**
 * Test script for the External API Service
 * 
 * This script tests the External API Service by making requests to the external API
 * and logging the results to the console.
 * 
 * Usage:
 * 1. Open the reports page in a browser
 * 2. Open the browser console
 * 3. Run the following command:
 *    loadScript('services/test-external-api.js')
 */

// Helper function to load the script
function loadScript(src) {
  return new Promise((resolve, reject) => {
    const script = document.createElement('script');
    script.src = src;
    script.onload = resolve;
    script.onerror = reject;
    document.head.appendChild(script);
  });
}

// Test the External API Service
async function testExternalApiService() {
  console.log('Testing External API Service...');
  
  // Test credentials
  const credentials = {
    uid: '<EMAIL>',
    password: 'test'
  };
  
  try {
    // Test login
    console.log('Testing login...');
    const jsessionId = await window.externalApiService.login(
      credentials.uid,
      credentials.password
    );
    console.log('Login successful, JSESSIONID:', jsessionId.substring(0, 8) + '...');
    
    // Test report summary
    console.log('Testing report summary...');
    const reportSummary = await window.externalApiService.getReportSummary(
      '13782',
      credentials.uid,
      credentials.password
    );
    console.log('Report summary:', reportSummary);
    
    // Test report details
    console.log('Testing report details...');
    const reportDetails = await window.externalApiService.getReportDetails(
      '13782',
      1,
      credentials.uid,
      credentials.password
    );
    console.log('Report details:', reportDetails);
    
    console.log('All tests passed!');
  } catch (error) {
    console.error('Test failed:', error);
  }
}

// Run the tests
testExternalApiService();
