/**
 * Session ID Service for SmartTest Reports
 *
 * This service provides methods to get recent test session IDs from various sources:
 * 1. Local storage cache
 * 2. Database API (when available)
 * 3. Hardcoded fallback values for testing
 */

class SessionIdService {
  constructor() {
    this.cacheKey = 'smarttest_recent_session_ids';
    this.cacheTtl = 5 * 60 * 1000; // 5 minutes

    // Updated fallback IDs to more recent test sessions
    this.fallbackIds = ['13840', '13839', '13838', '13837', '13836', '13835', '13834', '13833', '13832', '13831'];

    // Clear any existing cache on initialization to ensure fresh data
    this.clearCache();
    console.log('Session ID Service initialized with updated fallback IDs');
  }

  /**
   * Get recent test session IDs from all available sources
   * @param {Object} credentials - User credentials
   * @param {number} limit - Maximum number of IDs to return
   * @returns {Promise<Array<string>>} - Array of test session IDs
   */
  async getRecentSessionIds(credentials, limit = 10) {
    try {
      console.log('Getting recent test session IDs...');

      // Try to get IDs from cache first
      const cachedIds = this.getCachedSessionIds();
      if (cachedIds && cachedIds.length > 0) {
        console.log(`Using ${cachedIds.length} cached session IDs`);
        return cachedIds.slice(0, limit);
      }

      // Try to get IDs from database API
      try {
        const apiIds = await this.getSessionIdsFromApi(credentials, limit);
        if (apiIds && apiIds.length > 0) {
          console.log(`Got ${apiIds.length} session IDs from API`);
          this.cacheSessionIds(apiIds);
          return apiIds;
        }
      } catch (apiError) {
        console.error('Error getting session IDs from API:', apiError);
      }

      // Fall back to hardcoded IDs
      console.log('Using fallback session IDs');
      return this.fallbackIds.slice(0, limit);
    } catch (error) {
      console.error('Error getting recent session IDs:', error);
      return this.fallbackIds.slice(0, limit);
    }
  }

  /**
   * Get cached session IDs from local storage
   * @returns {Array<string>|null} - Array of session IDs or null if cache is invalid
   */
  getCachedSessionIds() {
    try {
      const cacheJson = localStorage.getItem(this.cacheKey);
      if (!cacheJson) return null;

      const cache = JSON.parse(cacheJson);

      // Check if cache is expired
      if (cache.expiry < Date.now()) {
        console.log('Session ID cache expired');
        localStorage.removeItem(this.cacheKey);
        return null;
      }

      return cache.ids;
    } catch (error) {
      console.error('Error reading session ID cache:', error);
      return null;
    }
  }

  /**
   * Cache session IDs in local storage
   * @param {Array<string>} ids - Array of session IDs to cache
   */
  cacheSessionIds(ids) {
    try {
      const cache = {
        ids: ids,
        expiry: Date.now() + this.cacheTtl
      };

      localStorage.setItem(this.cacheKey, JSON.stringify(cache));
      console.log(`Cached ${ids.length} session IDs`);
    } catch (error) {
      console.error('Error caching session IDs:', error);
    }
  }

  /**
   * Clear the session ID cache
   */
  clearCache() {
    try {
      localStorage.removeItem(this.cacheKey);
      console.log('Session ID cache cleared');
    } catch (error) {
      console.error('Error clearing session ID cache:', error);
    }
  }

  /**
   * Get session IDs from the database API
   * @param {Object} credentials - User credentials
   * @param {number} limit - Maximum number of IDs to return
   * @returns {Promise<Array<string>>} - Array of session IDs
   */
  async getSessionIdsFromApi(credentials, limit = 10) {
    try {
      // Use ApiService if available, otherwise fall back to direct fetch
      if (window.apiService) {
        console.log(`Using ApiService to fetch session IDs with limit ${limit}`);

        // Set credentials if they're not already set
        if (!window.apiService.credentials.uid) {
          window.apiService.setCredentials(credentials.uid, credentials.password);
        }

        // Use the getRecentRuns method we added to ApiService
        const recentRuns = await window.apiService.getRecentRuns({ limit });

        // Extract session IDs from the response
        const sessionIds = (recentRuns || [])
          .filter(run => run.tsn_id)
          .map(run => run.tsn_id.toString());

        return sessionIds;
      } else {
        // Fallback to original implementation for backward compatibility
        console.log(`ApiService not available, using direct fetch with limit ${limit}`);

        // Build URL for the recent runs endpoint
        const url = new URL('/local/recent-runs', window.location.origin);

        // Add authentication parameters
        url.searchParams.append('uid', credentials.uid);
        url.searchParams.append('password', credentials.password);
        url.searchParams.append('limit', limit);

        console.log(`Fetching session IDs from ${url.pathname}`);

        // Make the request
        const response = await fetch(url.toString());

        if (!response.ok) {
          throw new Error(`API request failed with status ${response.status}`);
        }

        const data = await response.json();

        // Check for success in the response
        if (!data.success) {
          throw new Error(data.message || 'API error');
        }

        // Extract session IDs from the response
        const sessionIds = (data.data || [])
          .filter(run => run.tsn_id)
          .map(run => run.tsn_id.toString());

        return sessionIds;
      }
    } catch (error) {
      console.error('Error getting session IDs from API:', error);
      throw error;
    }
  }
}

// Create global instance
window.sessionIdService = new SessionIdService();
