/**
 * SmartTest Dashboard - API Integration
 *
 * This file demonstrates how to use the API service
 * within the dashboard to interact with the test automation system.
 */

// Ensure API service is available
if (!window.apiService) {
  console.error('API Service not found! Make sure api-service.js is loaded before this file.');
}

// Dashboard API Integration
class DashboardApiIntegration {
  /**
   * Constructor initializes the API integration
   */
  constructor() {
    this.initialized = false;
    console.log('DashboardApiIntegration created');

    // Safe access to ApiService
    if (typeof ApiService === 'undefined') {
      console.error('ApiService class is not defined. Please make sure api-service.js is loaded first.');
      return;
    }

    // Safe access to apiService instance
    if (!window.apiService) {
      console.log('Creating new ApiService instance for DashboardApiIntegration');
      window.apiService = new ApiService();
    }

    this.apiService = window.apiService;
    this.notifications = {
      info: (message, title, duration) => {
        console.log(`INFO: ${title} - ${message}`);
        window.showNotification(title, message, 'info', duration);
      },
      success: (message, title, duration) => {
        console.log(`SUCCESS: ${title} - ${message}`);
        window.showNotification(title, message, 'success', duration);
      },
      warning: (message, title, duration) => {
        console.log(`WARNING: ${title} - ${message}`);
        window.showNotification(title, message, 'warning', duration);
      },
      error: (message, title, duration) => {
        console.error(`ERROR: ${title} - ${message}`);
        window.showNotification(title, message, 'error', duration);
      }
    };

    this.activeTests = new Map(); // Map of active test runs (tsnId -> status)
    this.statusPollingInterval = null;

    // Test suites will be loaded from the API
    this.predefinedSuites = {};

    // Available test cases for custom suite builder
    this.availableTestCases = [];
  }

  /**
   * Initialize the API integration
   */
  async initialize() {
    // Load credentials if not already loaded
    if (!this.apiService.credentials.uid || !this.apiService.credentials.password) {
      const credentialsLoaded = this.apiService.loadCredentials();

      if (!credentialsLoaded) {
        // Show login form if credentials aren't available
        this.showLoginForm();
        return false;
      } else {
        // If credentials were loaded, update UI to show logged in state
        this.handleSuccessfulLogin(this.apiService.credentials.uid);
      }
    } else {
      // If credentials are already available, update UI to show logged in state
      this.handleSuccessfulLogin(this.apiService.credentials.uid);
    }

    // Start polling for active test statuses
    this.startStatusPolling();

    // Load initial data
    await this.loadDashboardData();

    // Setup event listeners for predefined suites
    this.setupEventListeners();

    return true;
  }

  /**
   * Load initial dashboard data
   */
  async loadDashboardData() {
    try {
      console.log('Loading dashboard data...');

      // First load test suites
      await this.loadTestSuites();

      // Then load test cases
      await this.loadAvailableTestCases();

      // Load recent test runs (using the local endpoint)
      try {
        console.log('Loading recent test runs...');
        // Call the method that uses the correct local endpoint
        const response = await this.apiService.getRequest('/local/recent-runs');
        console.log('Recent runs response:', response);

        // Only handle the new refactored API response format
        const recentRuns = response.success && Array.isArray(response.data) ? response.data : [];

        // First update the dashboard counters based on recent runs
        this.updateDashboardCountersFromRecentRuns(recentRuns);

        // Then render the recent tests table
        this.renderRecentTests(recentRuns);
      } catch (error) {
        console.error('Error loading recent runs:', error);
        // Show error message instead of using mock data
        this.showError('Failed to load recent test runs. Please check your connection and credentials.');
        this.renderRecentTests([]);
      }
    } catch (error) {
      console.error('Error loading dashboard data:', error);
      this.showError('Failed to load dashboard data. Please check your connection and credentials.');
    }
  }

  /**
   * Load test suites
   */
  async loadTestSuites() {
    try {
      console.log('Loading test suites...');
      const response = await this.apiService.getRequest('/local/test-suites');
      console.log('Test suites response:', response);

      // Extract suites from the response - only handle the new refactored API response format
      const suites = response.success && Array.isArray(response.data) ? response.data : [];

      this.testSuites = suites;
      this.renderTestSuites();
      return suites;
    } catch (error) {
      console.error('Error getting test suites:', error);
      this.showError('Failed to load test suites.');
      return [];
    }
  }

  /**
   * Render test suites in the UI
   */
  renderTestSuites() {
    console.log('Rendering test suites:', this.testSuites);

    if (!this.testSuites || this.testSuites.length === 0) {
      console.log('No test suites to render');
      return;
    }

    // Get the container element
    const container = document.getElementById('test-suites-container');
    if (!container) {
      console.warn('Test suites container not found');
      return;
    }

    // Clear existing content
    container.innerHTML = '';

    // Create cards for each test suite
    this.testSuites.forEach(suite => {
      // Create card
      const card = document.createElement('div');
      card.className = 'ms-Card test-suite-card';

      // Set card content
      card.innerHTML = `
        <div class="ms-Card-header">
          <h3 class="ms-Card-title">${suite.name || `Suite ${suite.ts_id}`}</h3>
        </div>
        <div class="ms-Card-content">
          <p>${suite.comments || 'No description available'}</p>
          <div class="ms-Card-details">
            <span class="ms-Card-detail">ID: ${suite.ts_id}</span>
            <span class="ms-Card-detail">Status: ${suite.status || 'Unknown'}</span>
            <span class="ms-Card-detail">Owner: ${suite.uid || 'Unknown'}</span>
          </div>
        </div>
        <div class="ms-Card-actions">
          <button class="ms-Button ms-Button--primary run-suite-btn" data-suite-id="${suite.ts_id}" data-suite-name="${suite.name || `Suite ${suite.ts_id}`}">
            <span class="ms-Button-label">Run Suite</span>
          </button>
        </div>
      `;

      // Add to container
      container.appendChild(card);

      // Add event listener to run button
      const runButton = card.querySelector('.run-suite-btn');
      if (runButton) {
        runButton.addEventListener('click', () => {
          const suiteId = runButton.getAttribute('data-suite-id');
          const suiteName = runButton.getAttribute('data-suite-name');
          this.runTestSuite(suiteId, { name: suiteName });
        });
      }
    });
  }

  /**
   * Load available test cases
   */
  async loadAvailableTestCases() {
    try {
      // Show loading indicator in the container
      const container = document.getElementById('available-testcases');
      if (container) {
        container.innerHTML = '<div class="ms-loading">Loading test cases...</div>';
      }

      // Check for cached test cases in session storage
      const cachedCases = sessionStorage.getItem('smarttest_test_cases');
      if (cachedCases) {
        try {
          const parsedCases = JSON.parse(cachedCases);
          console.log('Using cached test cases from session storage');
          this.availableTestCases = parsedCases;
          
          // Render test cases asynchronously to prevent UI blocking
          setTimeout(() => this.renderAvailableTestCasesOptimized(parsedCases), 0);
          
          // Load fresh cases in the background for next time
          this.loadFreshTestCases();
          
          return parsedCases;
        } catch (parseError) {
          console.error('Error parsing cached test cases:', parseError);
          // Continue with fresh load if cache parsing fails
        }
      }
      
      return this.loadFreshTestCases();
    } catch (error) {
      console.error('Error in loadAvailableTestCases:', error);
      this.showError('Failed to load test cases.');
      return [];
    }
  }

  /**
   * Load fresh test cases from the API
   * @returns {Promise<Array>} - List of test cases
   */
  async loadFreshTestCases() {
    try {
      console.log('Loading fresh test cases from API...');
      console.time('testCasesLoad');
      
      const response = await this.apiService.getRequest('/local/test-cases');
      console.timeEnd('testCasesLoad');
      
      // Extract cases from the response
      const cases = response.success && Array.isArray(response.data) ? response.data : [];
      console.log(`Loaded ${cases.length} test cases`);
      
      // Save to session storage for future use
      try {
        sessionStorage.setItem('smarttest_test_cases', JSON.stringify(cases));
      } catch (cacheError) {
        console.warn('Could not cache test cases:', cacheError);
      }
      
      this.availableTestCases = cases;
      
      // Render test cases asynchronously to prevent UI blocking
      setTimeout(() => this.renderAvailableTestCasesOptimized(cases), 0);
      
      return cases;
    } catch (error) {
      console.error('Error loading fresh test cases:', error);
      this.showError('Failed to load test cases from server.');
      
      // Clear container loading state
      const container = document.getElementById('available-testcases');
      if (container) {
        container.innerHTML = '<div class="ms-empty-message">Failed to load test cases. Please try again.</div>';
      }
      
      return [];
    }
  }

  /**
   * Render available test cases for custom suite builder
   * @param {Array} testCases - List of available test cases
   */
  renderAvailableTestCasesOptimized(testCases) {
    const container = document.getElementById('available-testcases');
    if (!container) return;

    // Clear container
    container.innerHTML = '';

    if (!testCases || testCases.length === 0) {
      container.innerHTML = '<div class="ms-empty-message">No test cases available.</div>';
      return;
    }

    // Add each test case as a checkbox
    testCases.forEach(testCase => {
      const item = document.createElement('div');
      item.className = 'ms-checkbox-item';

      // Create unique ID for the checkbox
      const checkboxId = `tc-${testCase.id}`;

      // Create label for the test case name
      const label = document.createElement('label');
      label.htmlFor = checkboxId;
      label.textContent = testCase.name || `Test Case ${testCase.id}`;

      // Create checkbox container
      const checkboxContainer = document.createElement('div');
      checkboxContainer.className = 'checkbox-container';

      // Create the checkbox input
      const checkbox = document.createElement('input');
      checkbox.type = 'checkbox';
      checkbox.id = checkboxId;
      checkbox.value = testCase.id;
      checkbox.className = 'ms-checkbox';
      checkbox.setAttribute('aria-label', `Select ${testCase.name || `Test Case ${testCase.id}`}`);

      // Add checkbox to its container
      checkboxContainer.appendChild(checkbox);

      // Add elements to the item
      item.appendChild(label);
      item.appendChild(checkboxContainer);

      // Add description if available
      if (testCase.description) {
        const description = document.createElement('div');
        description.className = 'ms-test-description';
        description.textContent = testCase.description;
        item.appendChild(description);
      }

      // Add click handler to the entire item for better usability
      item.addEventListener('click', (e) => {
        // Only toggle if the click wasn't directly on the checkbox or label
        // (to avoid double-toggling)
        if (e.target !== checkbox && e.target !== label) {
          checkbox.checked = !checkbox.checked;

          // Trigger change event to ensure any listeners are notified
          const event = new Event('change', { bubbles: true });
          checkbox.dispatchEvent(event);
        }
      });

      // Add the complete item to the container
      container.appendChild(item);
    });
  }

  /**
   * Run a test case
   * @param {string} tcId - The test case ID to run
   * @param {Object} params - Optional parameters for the test case
   * @returns {Promise<Object>} - The response from the API
   */
  async runTestCase(tcId, params = {}) {
    try {
      this.notifications.info(`Starting test case ${tcId}...`, 'Test Started');
      
      // Temporarily increment counter for UI feedback
      this.incrementCounter('running-tests');
      this.incrementCounter('total-tests');
      
      // Make the API call to run the test case
      const response = await this.apiService.runTestCase(tcId, params);
      
      if (response && response.tsn_id) {
        console.log(`Test case ${tcId} started with session ID: ${response.tsn_id}`);
        this.notifications.success(`Test case ${tcId} started successfully`, 'Test Started');
        
        // Add to active tests
        this.activeTests.set(response.tsn_id, {
          tcId: tcId,
          status: 'Running',
          startTime: new Date().toISOString()
        });
        
        // Refresh the dashboard to show latest data
        setTimeout(() => this.refreshRecentRuns(), 3000); // Refresh after 3 seconds
        
        return response;
      } else {
        console.error(`Failed to start test case ${tcId}:`, response);
        this.notifications.error(`Failed to start test case ${tcId}`, 'Test Error');
        
        // Decrement the running counter since the test didn't start
        this.incrementCounter('running-tests', -1);
        return null;
      }
    } catch (error) {
      console.error(`Error running test case ${tcId}:`, error);
      this.notifications.error(`Error running test case ${tcId}: ${error.message || 'Unknown error'}`, 'Test Error');
      
      // Decrement the counters since the test didn't start
      this.incrementCounter('running-tests', -1);
      this.incrementCounter('total-tests', -1);
      
      throw error;
    } finally {
      // Refresh the dashboard to show latest data
      setTimeout(() => this.refreshRecentRuns(), 3000); // Refresh after 3 seconds
    }
  }

  /**
   * Render recent test runs
   * @param {Array} recentTests - List of recent test runs
   */
  renderRecentTests(recentTests) {
    console.log('Rendering recent tests:', recentTests);

    if (!recentTests || recentTests.length === 0) {
      console.log('No recent tests to render');
      return;
    }

    // Get the container element
    const container = document.querySelector('.recent-tests-table tbody');
    if (!container) {
      console.warn('Recent tests container not found');
      return;
    }

    // Clear existing content
    container.innerHTML = '';

    // Create rows for each test
    recentTests.forEach(test => {
      // Format date
      const date = new Date(test.created_at || test.start_ts || new Date());
      const formattedDate = date.toLocaleString();

      // Create row
      const row = document.createElement('tr');

      // Set row content based on the available fields in the test object
      row.innerHTML = `
        <td>${test.tc_id || test.id || 'N/A'}</td>
        <td>${test.tsn_id || test.id || 'N/A'}</td>
        <td>${test.uid || test.user || 'N/A'}</td>
        <td>
          <span class="status-badge status-${test.status || 'unknown'}">${test.status || 'Unknown'}</span>
        </td>
        <td>${formattedDate}</td>
        <td>
          <button class="ms-Button ms-Button--primary view-details" data-tsn-id="${test.tsn_id || test.id || '0'}">
            <span class="ms-Button-label">View Details</span>
          </button>
        </td>
      `;

      container.appendChild(row);
    });

    // Update test counter
    const totalTestsElement = document.getElementById('total-tests');
    if (totalTestsElement) {
      totalTestsElement.textContent = recentTests.length;
    }

    // Add event listeners for view details buttons
    this.attachViewDetailsHandlers();
  }

  /**
   * Updates the active tests panel with new content
   * @param {string} content - HTML content to display
   */
  updateActiveTests(content) {
    const activeTestsContainer = document.getElementById('active-tests-container');

    if (activeTestsContainer) {
      // Find or create the message container
      let messageContainer = activeTestsContainer.querySelector('.ms-empty-message');

      if (!messageContainer) {
        messageContainer = document.createElement('div');
        messageContainer.className = 'ms-empty-message';
        activeTestsContainer.appendChild(messageContainer);
      }

      // Update the content
      messageContainer.innerHTML = content;
      console.log('Updated active tests panel with content:', content);
    } else {
      console.warn('Active tests container not found in DOM');
    }
  }

  /**
   * Increments a dashboard counter by the specified amount
   * @param {string} counterType - The counter to increment ('total-tests', 'running-tests', etc.)
   * @param {number} amount - Amount to increment by (default: 1)
   */
  incrementCounter(counterType, amount = 1) {
    const counterElement = document.getElementById(counterType);
    if (counterElement) {
      const currentValue = parseInt(counterElement.textContent, 10) || 0;
      counterElement.textContent = (currentValue + amount).toString();
    }
  }

  /**
   * Refreshes the list of recent test runs
   */
  async refreshRecentRuns() {
    try {
      const response = await this.apiService.getRequest('/local/recent-runs');
      
      if (response && response.success) {
        console.log(`Recent runs loaded: ${response.data.length} tests`);
        
        // Update dashboard counters based on the latest data
        this.updateDashboardCountersFromRecentRuns(response.data);
        
        // Update the UI with the new data
        this.updateRecentRunsTable(response.data);
        
        return response.data;
      } else {
        throw new Error('Failed to load recent runs');
      }
    } catch (error) {
      console.error('Error refreshing recent runs:', error);
      return [];
    }
  }

  /**
   * Update the recent runs table with data
   * @param {HTMLElement} table - The table element
   * @param {Array} recentRuns - The recent runs data
   */
  updateRecentRunsTable(recentRuns = []) {
    console.log('Updating recent runs table with data:', recentRuns);

    try {
      // Find the table - try multiple selector strategies
      let recentRunsTable = null;

      // Strategy 1: Look for table with Test ID header
      const tables = document.querySelectorAll('table');
      for (const table of tables) {
        const headers = table.querySelectorAll('th');
        for (const header of headers) {
          if (header.textContent.includes('Test ID')) {
            recentRunsTable = table;
            console.log('Found recent runs table using Test ID header');
            break;
          }
        }
        if (recentRunsTable) break;
      }

      // Strategy 2: Look for table with specific class or id
      if (!recentRunsTable) {
        recentRunsTable = document.querySelector('.recent-runs-table') ||
                          document.querySelector('#recent-runs-table');
        if (recentRunsTable) {
          console.log('Found recent runs table using class/id selector');
        }
      }

      // Strategy 3: Look for any table within the dashboard content area
      if (!recentRunsTable) {
        const dashboardContent = document.querySelector('.dashboard-content') ||
                                document.querySelector('#dashboard-content');
        if (dashboardContent) {
          recentRunsTable = dashboardContent.querySelector('table');
          if (recentRunsTable) {
            console.log('Found recent runs table within dashboard content');
          }
        }
      }

      // Strategy 4: Last resort - take the first table in the document
      if (!recentRunsTable) {
        recentRunsTable = document.querySelector('table');
        if (recentRunsTable) {
          console.log('Using first table as recent runs table (fallback)');
        }
      }

      if (!recentRunsTable) {
        console.error('Recent runs table not found');
        return;
      }

      // Get or create tbody
      let tableBody = recentRunsTable.querySelector('tbody');
      if (!tableBody) {
        tableBody = document.createElement('tbody');
        recentRunsTable.appendChild(tableBody);
      }

      // Clear existing rows
      tableBody.innerHTML = '';

      // Add a "no data" row if no recent runs
      if (!recentRuns || recentRuns.length === 0) {
        const noDataRow = document.createElement('tr');
        const headerRow = recentRunsTable.querySelector('thead tr');
        const columns = headerRow ? headerRow.querySelectorAll('th').length : 7;

        const noDataCell = document.createElement('td');
        noDataCell.setAttribute('colspan', columns);
        noDataCell.textContent = 'No recent test runs found';
        noDataCell.style.textAlign = 'center';
        noDataCell.style.padding = '10px';

        noDataRow.appendChild(noDataCell);
        tableBody.appendChild(noDataRow);
        return;
      }

      // Add new rows
      recentRuns.forEach(run => {
        const row = document.createElement('tr');

        // Format the date
        let formattedDate = 'N/A';
        try {
          formattedDate = this.formatTime(run.start_time || run.created_at || run.timestamp);
        } catch (e) {
          console.error('Error formatting date:', e);
        }

        // Get the test ID and name
        const testId = run.tc_id || run.ts_id || run.test_id || 'N/A';
        const testName = run.test_name || run.name || `Test ${testId}`;

        row.innerHTML = `
          <td>${testId}</td>
          <td>${run.tsn_id || run.run_id || 'N/A'}</td>
          <td>${testName}</td>
          <td>${run.user_id || run.uid || 'N/A'}</td>
          <td><span class="status-${run.status || 'unknown'}">${run.status || 'unknown'}</span></td>
          <td>${formattedDate}</td>
          <td>
            <button class="ms-Button ms-Button--primary view-details" data-tsn-id="${run.tsn_id || run.run_id || ''}">
              <span class="ms-Button-label">View Details</span>
            </button>
          </td>
        `;

        tableBody.appendChild(row);
      });

      // Attach event listeners to the "View Details" buttons
      this.attachViewDetailsHandlers();

      console.log('Recent runs table updated successfully');
    } catch (error) {
      console.error('Error updating recent runs table:', error);
    }
  }

  /**
   * Attach event listeners to "View Details" buttons
   */
  attachViewDetailsHandlers() {
    document.querySelectorAll('.view-details').forEach(button => {
      button.addEventListener('click', (event) => {
        const tsnId = event.currentTarget.getAttribute('data-tsn-id');
        if (tsnId) {
          this.viewTestDetails(tsnId);
        }
      });
    });
  }

  /**
   * View details for a specific test run
   * @param {string} tsnId - The test session ID to view
   */
  async viewTestDetails(tsnId) {
    try {
      // Check if tsnId is valid
      if (!tsnId || tsnId === 'undefined' || tsnId === 'null') {
        this.showError(`Cannot load report: Invalid test session ID (${tsnId}).`);
        return null;
      }

      const response = await this.apiService.getRequest(`/local/test-details/${tsnId}`);

      // Only handle the new refactored API response format
      const details = response.success && response.data ? response.data : {};

      // Create a dialog to display test details
      const dialog = document.createElement('div');
      dialog.className = 'ms-Dialog ms-Dialog--close';
      dialog.innerHTML = `
        <div class="ms-Dialog-title">Test Run Details - ${tsnId}</div>
        <div class="ms-Dialog-content">
          <div class="ms-Grid">
            <div class="ms-Grid-row">
              <div class="ms-Grid-col ms-sm6">
                <h3>Test Case: ${details.tc_id}</h3>
                <p>Status: <span class="ms-fontColor-${this.getStatusColor(details.status)}">${details.status}</span></p>
                <p>Started: ${new Date(details.start_ts).toLocaleString()}</p>
                <p>User: ${details.uid}</p>
              </div>
              <div class="ms-Grid-col ms-sm6">
                <h3>Results</h3>
                <pre class="ms-font-xs">${JSON.stringify(details.results || {}, null, 2)}</pre>
              </div>
            </div>
          </div>
        </div>
        <div class="ms-Dialog-actions">
          <button class="ms-Button ms-Dialog-action ms-Button--primary">
            <span class="ms-Button-label">Close</span>
          </button>
        </div>
      `;

      document.body.appendChild(dialog);

      // Initialize the dialog component
      const dialogComponent = new fabric.Dialog(dialog);
      dialogComponent.open();

      // Add event listener to close button
      dialog.querySelector('.ms-Dialog-action').addEventListener('click', () => {
        dialogComponent.close();
        setTimeout(() => {
          document.body.removeChild(dialog);
        }, 500);
      });

    } catch (error) {
      console.error(`Error viewing test details for ${tsnId}:`, error);
      this.notifications.error(
        `Failed to load test details: ${error.message}`,
        'Error',
        3000
      );
    }
  }

  /**
   * Get color class based on test status
   * @param {string} status - Test status
   * @returns {string} - Color class
   */
  getStatusColor(status) {
    const statusColors = {
      running: 'blue',
      passed: 'green',
      failed: 'red',
      error: 'redDark',
      pending: 'orange'
    };

    return statusColors[status.toLowerCase()] || 'neutralSecondary';
  }

  /**
   * Run a test suite
   * @param {number} tsId - Test suite ID
   * @param {Object} params - Additional parameters
   */
  async runTestSuite(tsId, params = {}) {
    try {
      // Show loading indicator
      this.showLoading(`Running test suite ${tsId}...`);

      // Run the test suite
      const tsnId = await this.apiService.runTestSuite(tsId, params);

      if (tsnId) {
        // Add to active tests
        this.activeTests.set(tsnId, {
          status: 'running',
          type: 'suite',
          id: tsId,
          startTime: new Date(),
          name: params.name || `Test Suite ${tsId}`
        });

        // Show success message
        this.showSuccess(`Test suite ${tsId} started successfully. Run ID: ${tsnId}`);

        // Update the UI
        this.renderActiveTests();

        return tsnId;
      } else {
        throw new Error('Could not get test run ID');
      }
    } catch (error) {
      console.error('Error running test suite:', error);
      this.showError(`Failed to run test suite ${tsId}: ${error.message}`);
      return null;
    } finally {
      this.hideLoading();
    }
  }

  /**
   * Stop a running test
   * @param {number} tsnId - Test suite run ID
   */
  async stopTest(tsnId) {
    try {
      // Show loading indicator
      this.showLoading(`Stopping test run ${tsnId}...`);

      // Stop the test
      const success = await this.apiService.stopTest(tsnId);

      if (success) {
        // Update active test status
        const test = this.activeTests.get(tsnId);
        if (test) {
          test.status = 'stopped';
          this.activeTests.set(tsnId, test);
        }

        // Show success message
        this.showSuccess(`Test run ${tsnId} stopped successfully.`);

        // Update the UI
        this.renderActiveTests();

        return true;
      } else {
        throw new Error('Failed to stop test');
      }
    } catch (error) {
      console.error('Error stopping test:', error);
      this.showError(`Failed to stop test run ${tsnId}: ${error.message}`);
      return false;
    } finally {
      this.hideLoading();
    }
  }

  /**
   * View test report
   * @param {number|string} tsnId - Test suite run ID
   */
  async viewTestReport(tsnId) {
    try {
      // Check if tsnId is valid
      if (!tsnId || tsnId === 'undefined' || tsnId === 'null') {
        this.showError(`Cannot load report: Invalid test session ID (${tsnId}).`);
        return null;
      }

      // Make sure tsnId is treated as a string (to avoid type issues)
      const sessionId = String(tsnId).trim();

      // Show loading indicator
      this.showLoading(`Loading report for test run ${sessionId}...`);

      // Log the attempt to get the report
      console.log(`Attempting to fetch report for session ${sessionId}`);

      try {
        // Get the report summary with timeout handling
        const reportPromise = this.apiService.getReportSummary(sessionId);

        // Set a timeout to handle hanging requests
        const timeoutPromise = new Promise((_, reject) => {
          setTimeout(() => reject(new Error('Report request timed out')), 15000);
        });

        // Use Promise.race to handle timeouts
        const response = await Promise.race([reportPromise, timeoutPromise]);

        // Only handle the new refactored API response format
        const report = response.success && response.data ? response.data : {};

        // If report was successfully retrieved, show it
        if (report) {
          console.log(`Report data retrieved for session ${sessionId}:`, report);
          this.showReport(sessionId, report);
          return report;
        } else {
          throw new Error('No report data received');
        }
      } catch (reportError) {
        console.error(`Error fetching report for ${sessionId}:`, reportError);

        // Redirect to the reports page with the test ID parameter instead of API URL
        const reportsPageUrl = `/reports/index.html?tsn_id=${sessionId}`;

        console.log(`Redirecting to reports page: ${reportsPageUrl}`);
        window.open(reportsPageUrl, '_blank');

        this.showWarning(`Report viewer had an error, opening reports page in new tab...`);
        return null;
      }
    } catch (error) {
      console.error(`Error viewing report for ${tsnId}:`, error);
      this.showError(`Failed to load report for test run ${tsnId}: ${error.message}`);
      return null;
    } finally {
      this.hideLoading();
    }
  }

  /**
   * Rerun failed tests from a specific test run
   * @param {number} tsnId - Original test suite run ID
   * @param {Object} params - Additional parameters
   * @returns {Promise<number>} - New test suite run ID
   */
  async rerunFailedTests(tsnId, params = {}) {
    try {
      // Show loading indicator
      this.showLoading(`Preparing to rerun failed tests from run ${tsnId}...`);

      // Rerun the failed tests
      const newTsnId = await this.apiService.rerunFailedTests(tsnId, params);

      if (newTsnId) {
        // Add to active tests
        this.activeTests.set(newTsnId, {
          status: 'running',
          type: 'rerun',
          originalId: tsnId,
          startTime: new Date(),
          name: params.name || `Rerun of Failed Tests (${tsnId})`
        });

        // Show success message
        this.showSuccess(`Failed tests from run ${tsnId} started successfully. New Run ID: ${newTsnId}`);

        // Update the UI
        this.renderActiveTests();

        return newTsnId;
      } else {
        throw new Error('Could not get test run ID for rerun');
      }
    } catch (error) {
      console.error('Error rerunning failed tests:', error);
      this.showError(`Failed to rerun failed tests from ${tsnId}: ${error.message}`);
      return null;
    } finally {
      this.hideLoading();
    }
  }

  /**
   * Start polling for test status updates
   */
  startStatusPolling() {
    // Clear any existing interval
    if (this.statusPollingInterval) {
      clearInterval(this.statusPollingInterval);
    }

    // Poll every 5 seconds
    this.statusPollingInterval = setInterval(() => {
      this.updateTestStatuses();
    }, 5000);
  }

  /**
   * Update the status of all active tests
   */
  async updateTestStatuses() {
    let needsUIRefresh = false;
    let needsDashboardUpdate = false;

    // Check each active test
    for (const [tsnId, test] of this.activeTests.entries()) {
      // Only check tests that are still running or haven't been marked as completed
      if (test.status === 'running' || !['passed', 'failed', 'completed'].includes(test.status)) {
        try {
          const status = await this.apiService.getTestStatus(tsnId);
          console.log(`Status update for test ${tsnId}:`, status);

          // Update status
          if (status && status.success) {
            // Check if status has changed
            const oldStatus = test.status;
            
            // Determine test status based on API response
            if (status.status === 'fail' || status.status === 'failed') {
              test.status = 'failed';
              test.outcome = 'failed';
            } else if (status.status === 'pass' || status.status === 'passed') {
              test.status = 'passed';
              test.outcome = 'passed';
            } else if (status.end_time) {
              // If end_time exists but status is ambiguous, determine based on passed/failed counts
              if (status.failed > 0) {
                test.status = 'failed';
                test.outcome = 'failed';
              } else if (status.passed > 0) {
                test.status = 'passed';
                test.outcome = 'passed';
              } else {
                test.status = 'completed';
              }
            } else {
              test.status = status.status || 'running';
            }

            // If test just completed, update dashboard counters
            if (oldStatus === 'running' && ['completed', 'passed', 'failed'].includes(test.status)) {
              needsDashboardUpdate = true;
            }

            // Always update UI when we get a status update
            needsUIRefresh = true;

            // Update other test details if available
            if (status.progress !== undefined) test.progress = status.progress;
            if (status.message) test.message = status.message;
            if (status.start_time) test.startTime = new Date(status.start_time);
            if (status.end_time) test.endTime = new Date(status.end_time);
            if (status.passed !== undefined) test.passed = status.passed;
            if (status.failed !== undefined) test.failed = status.failed;
            
            // If test is completed, stop polling after a short delay
            if (test.endTime || ['passed', 'failed', 'completed'].includes(test.status)) {
              console.log(`Test ${tsnId} completed with status: ${test.status}`);
              
              // Schedule test removal from active tests after showing result
              setTimeout(() => {
                if (this.activeTests.has(tsnId)) {
                  console.log(`Removing completed test ${tsnId} from active tests`);
                  this.activeTests.delete(tsnId);
                  this.renderActiveTests();
                  this.updateDashboardCounters();
                }
              }, 10000); // Keep showing result for 10 seconds
            }
          }
        } catch (error) {
          console.error(`Error updating status for test ${tsnId}:`, error);
        }
      }
    }

    // If any test status has changed, re-render the entire active tests section
    if (needsUIRefresh) {
      this.renderActiveTests();
    }

    // If any test has completed, update the dashboard counters
    if (needsDashboardUpdate) {
      this.updateDashboardCounters();
    }
  }

  /**
   * Update dashboard counters based on active tests and recent runs
   */
  updateDashboardCounters() {
    try {
      // Get counters from DOM
      const totalCounter = document.getElementById('total-tests');
      const successfulCounter = document.getElementById('successful-tests');
      const failedCounter = document.getElementById('failed-tests');

      if (!totalCounter || !successfulCounter || !failedCounter) {
        console.warn('Dashboard counters not found in DOM');
        return;
      }

      // Get current values
      let totalCount = parseInt(totalCounter.textContent, 10) || 0;
      let successfulCount = parseInt(successfulCounter.textContent, 10) || 0;
      let failedCount = parseInt(failedCounter.textContent, 10) || 0;

      // Count completed tests from our active tests map
      const completedTests = Array.from(this.activeTests.entries())
        .filter(([_, test]) =>
          ['completed', 'passed', 'failed'].includes(test.status) &&
          !test.countedInDashboard
        )
        .map(([id, test]) => {
          // Mark as counted so we don't count it again
          test.countedInDashboard = true;
          return { id, ...test };
        });

      if (completedTests.length === 0) {
        console.log('No new completed tests to count');
        return;
      }

      console.log(`Found ${completedTests.length} new completed tests to count in dashboard`);

      // Increment total for each completed test
      totalCount += completedTests.length;

      // Count passed and failed tests
      const passedTests = completedTests.filter(
        test => test.status === 'passed' || test.outcome === 'passed'
      );

      const failedTests = completedTests.filter(
        test => test.status === 'failed' || test.outcome === 'failed'
      );

      console.log(`Passed tests: ${passedTests.length}, Failed tests: ${failedTests.length}`);

      // Increment counters
      successfulCount += passedTests.length;
      failedCount += failedTests.length;

      // Update DOM
      totalCounter.textContent = totalCount;
      successfulCounter.textContent = successfulCount;
      failedCounter.textContent = failedCount;

      console.log('Updated dashboard counters:', {
        total: totalCount,
        successful: successfulCount,
        failed: failedCount
      });
    } catch (error) {
      console.error('Error updating dashboard counters:', error);
    }
  }

  /**
   * Update dashboard counters based on recent runs data
   * @param {Array} recentRuns - Recent test runs data
   */
  updateDashboardCountersFromRecentRuns(recentRuns) {
    try {
      console.log('Updating dashboard counters from recent runs data');
      
      if (!recentRuns || !Array.isArray(recentRuns)) {
        console.warn('No valid recent runs data to update counters');
        return;
      }
      
      // Filter runs to only those from the current user session
      const currentUser = this.apiService.credentials.uid;
      const userRuns = currentUser ? recentRuns.filter(run => run.uid === currentUser) : recentRuns;
      
      console.log(`Found ${userRuns.length} runs for current user: ${currentUser}`);
      
      // Calculate totals from recent runs
      const totalRuns = userRuns.length;
      const passedRuns = userRuns.filter(run => run.status && run.status.toLowerCase() === 'passed').length;
      const failedRuns = userRuns.filter(run => run.status && run.status.toLowerCase() === 'failed').length;
      const runningRuns = userRuns.filter(run => run.status && run.status.toLowerCase() === 'running').length;
      
      // Get counter elements
      const totalCounter = document.getElementById('total-tests');
      const passedCounter = document.getElementById('successful-tests');
      const failedCounter = document.getElementById('failed-tests');
      const runningCounter = document.getElementById('running-tests');
      
      // Update counter displays if elements exist
      if (totalCounter) totalCounter.textContent = totalRuns;
      if (passedCounter) passedCounter.textContent = passedRuns;
      if (failedCounter) failedCounter.textContent = failedRuns;
      if (runningCounter) runningCounter.textContent = runningRuns;
      
      console.log(`Dashboard counters updated: Total=${totalRuns}, Passed=${passedRuns}, Failed=${failedRuns}, Running=${runningRuns}`);
    } catch (error) {
      console.error('Error updating dashboard counters:', error);
    }
  }

  /**
   * Render active test runs (grouped by tsn_id)
   */
  async renderActiveTests() {
    const container = document.getElementById('active-tests-container');
    if (!container) return;
    try {
      // Get all active tests from API
      let activeTestsRaw = [];
      if (this.activeTests.size > 0) {
        activeTestsRaw = Array.from(this.activeTests.values());
        console.log('Active tests from map:', activeTestsRaw);
      } else if (this.apiService && this.apiService.getActiveTests) {
        // Get active tests from API - only handle the new refactored API response format
        activeTestsRaw = await this.apiService.getActiveTests();
        console.log('Active tests from API:', activeTestsRaw);
      }
      
      // Process each test to ensure it has all required fields
      const processedTests = activeTestsRaw.map(test => this.processTestData(test));
      console.log('Processed tests:', processedTests);
      
      // Group by tsn_id
      const grouped = {};
      processedTests.forEach(test => {
        const key = test.tsn_id || test.id;
        if (!grouped[key]) grouped[key] = [];
        grouped[key].push(test);
      });
      
      // Render one card per tsn_id
      container.innerHTML = '';
      Object.entries(grouped).forEach(([tsn_id, tests]) => {
        // Aggregate status/progress
        const passed = tests.filter(t => t.status === 'PASSED' || t.status === 'passed' || t.outcome === 'P' || t.outcome === 'passed').length;
        const failed = tests.filter(t => t.status === 'FAILED' || t.status === 'failed' || t.outcome === 'F' || t.outcome === 'failed' || t.failed > 0).length;
        const running = tests.filter(t => (t.status === 'RUNNING' || t.status === 'running') && !t.endTime).length;
        const total = tests[0].totalTests || tests.length;
        const progress = total === 0 ? 0 : Math.round(((passed + failed) / total) * 100);

        // For test suites, consider failures specifically
        const testInfo = tests[0]; // Use first test in group for details
        const isTestSuite = testInfo.type === 'Test Suite' || Boolean(testInfo.ts_id);
        
        // Check individual test metrics
        const hasFailedTests = isTestSuite && (
          testInfo.failed > 0 || 
          tests.some(t => t.failed > 0) || 
          testInfo.status === 'failed' || 
          testInfo.status === 'fail'
        );

        // Determine status for color coding
        let statusClass = 'pending';
        let statusText = 'Pending';
        
        // Determine if the test is still running or can be stopped
        // Tests should only be considered "finished" if they have a completed status
        const isActive = running || 
                         (!testInfo.endTime && 
                         testInfo.status !== 'passed' && 
                         testInfo.status !== 'failed' && 
                         testInfo.status !== 'stopped' &&
                         testInfo.status !== 'completed');
        
        if (running) {
          statusClass = 'running';
          statusText = 'Running';
        } else if (hasFailedTests || failed > 0) {
          statusClass = 'failed';
          statusText = 'Failed';
        } else if ((passed > 0 && failed === 0) || testInfo.status === 'passed') {
          statusClass = 'passed';
          statusText = 'Passed';
        }

        console.log(`Test status for ${tsn_id}:`, {
          statusClass, 
          statusText,
          hasFailedTests,
          passed,
          failed,
          running,
          isActive,
          testInfo
        });
        
        // Determine the correct name to display
        // Priority: suiteName > testName > name > "Test Suite/Case" + ID
        const testName = testInfo.suiteName ||
                      testInfo.testName ||
                      testInfo.name ||
                      (testInfo.ts_id ? `Test Suite ${testInfo.ts_id}` :
                      (testInfo.tc_id ? `Test Case ${testInfo.tc_id}` : 'Test Run'));

        // Get the correct ID for test suite/case
        // For test suites, use ts_id (e.g. 332)
        // For test cases, use tc_id
        const testId = testInfo.ts_id || testInfo.tc_id || 'Unknown';
        
        // For debugging
        console.log('Test info for rendering:', {
          isTestSuite,
          testId: testId,
          ts_id: testInfo.ts_id,
          tc_id: testInfo.tc_id,
          type: testInfo.type,
          status: testInfo.status,
          outcome: testInfo.outcome,
          failed: testInfo.failed
        });

        // Get user info
        const userName = testInfo.user || testInfo.userName || 'You';

        // Format timestamp
        const startTime = testInfo.startTime || testInfo.start_time || new Date().toISOString();
        const formattedTime = new Date(startTime).toLocaleString();

        // Ensure we have a valid tsn_id (string format)
        const sessionId = String(tsn_id);

        // Compose card
        const card = document.createElement('div');
        card.className = `test-card ${statusClass}`;
        card.setAttribute('data-tsn-id', sessionId);

        // Log the card data for debugging
        console.log(`Rendering card for session ${sessionId}:`, {
          testName,
          testId,
          sessionId,
          status: statusClass,
          statusText,
          progress,
          isTestSuite
        });

        card.innerHTML = `
          <div class="test-card-header">
            <div class="test-info">
              <h3 class="test-name">${testName}</h3>
              <div class="test-id-session">
                <span class="test-id-label">${isTestSuite ? 'Suite ID:' : 'ID:'}</span> <span class="test-id">${testId}</span>
                <span class="session-label">TSN ID:</span> <span class="session-id">${sessionId}</span>
              </div>
              <div class="test-user-time">
                <span class="test-user">${userName}</span>
                <span class="test-time">${formattedTime}</span>
              </div>
            </div>
            <span class="status-indicator ${statusClass}"></span>
            <span class="status-text">${statusText}</span>
          </div>
          <div class="test-card-body">
            <div class="progress-container">
              <div class="progress-bar ${statusClass}" style="width: ${progress}%;">${progress}%</div>
            </div>
            <div class="test-progress">${passed} / ${total} Passed, ${failed} Failed</div>
          </div>
          <div class="test-card-footer">
            <button class="btn btn-sm btn-primary view-report-btn" onclick="window.dashboardApiIntegration.viewTestReport('${sessionId}')">
              See Report
            </button>
            ${isActive ? `
              <button class="btn btn-sm btn-danger stop-button" onclick="window.dashboardApiIntegration.stopTest('${sessionId}')">
                Stop Test
              </button>
            ` : ''}
          </div>
        `;
        container.appendChild(card);
      });
      if (Object.keys(grouped).length === 0) {
        container.innerHTML = '<div class="ms-empty-message">No active tests running</div>';
      }
    } catch (error) {
      console.error('Error rendering active tests:', error);
      container.innerHTML = '<div class="ms-empty-message error">Error loading active tests</div>';
    }
  }

  /**
   * Show a test report
   * @param {number} tsnId - Test suite run ID
   * @param {Object} report - Report data
   */
  showReport(tsnId, report) {
    // This is a placeholder - implement actual UI in your dashboard
    console.log(`Showing report for test run ${tsnId}:`, report);

    // Example implementation:
    const modal = document.getElementById('report-modal');
    if (modal) {
      const content = modal.querySelector('#report-content');
      if (content) {
        // Format report as HTML
        let html;

        if (typeof report === 'string') {
          // If report is HTML, display it directly
          html = report;
        } else {
          // If report is an object, format it
          html = `
            <h2>Test Run Report: ${tsnId}</h2>
            <div class="report-summary">
              ${this.formatReportSummary(report)}
            </div>
          `;
        }

        content.innerHTML = html;
      }

      modal.style.display = 'block';
    }
  }

  /**
   * Format report summary as HTML
   * @param {Object} report - Report data
   * @returns {string} HTML representation of the report
   */
  formatReportSummary(report) {
    if (!report) return '<p>No report data available</p>';

    // Only handle the new refactored API response format
    if (report.testCases) {
      const testCases = report.testCases || [];
      const status = report.status || 'unknown';
      const duration = report.duration || 'N/A';

      const passedCount = testCases.filter(tc => tc.status === 'passed').length;
      const failedCount = testCases.filter(tc => tc.status === 'failed' || tc.status === 'error').length;
      const skippedCount = testCases.filter(tc => tc.status === 'skipped').length;

      return `
        <div class="report-stats">
          <div class="report-stat">
            <span class="stat-label">Status:</span>
            <span class="stat-value ${status === 'completed' ? 'text-success' : 'text-danger'}">${status}</span>
          </div>
          <div class="report-stat">
            <span class="stat-label">Total Tests:</span>
            <span class="stat-value">${testCases.length}</span>
          </div>
          <div class="report-stat">
            <span class="stat-label">Passed:</span>
            <span class="stat-value text-success">${passedCount}</span>
          </div>
          <div class="report-stat">
            <span class="stat-label">Failed:</span>
            <span class="stat-value text-danger">${failedCount}</span>
          </div>
          <div class="report-stat">
            <span class="stat-label">Skipped:</span>
            <span class="stat-value text-warning">${skippedCount}</span>
          </div>
          <div class="report-stat">
            <span class="stat-label">Duration:</span>
            <span class="stat-value">${duration ? `${duration}s` : 'N/A'}</span>
          </div>
        </div>

        <h3>Test Cases</h3>
        <table class="ms-Table">
          <thead>
            <tr>
              <th>ID</th>
              <th>Name</th>
              <th>Status</th>
              <th>Duration</th>
            </tr>
          </thead>
          <tbody>
            ${testCases.map(tc => `
              <tr class="${tc.status === 'failed' || tc.status === 'error' ? 'ms-bgColor-sharedRedLight10' : tc.status === 'skipped' ? 'ms-bgColor-sharedYellowLight10' : ''}">
                <td>${tc.id}</td>
                <td>${tc.name}</td>
                <td>${tc.status}</td>
                <td>${tc.duration}s</td>
              </tr>
              ${tc.errorMessage ? `
              <tr class="ms-bgColor-sharedRedLight10">
                <td colspan="4" class="error-message">
                  <strong>Error:</strong> ${tc.errorMessage}
                </td>
              </tr>
              ` : ''}
            `).join('')}
          </tbody>
        </table>
      `;
    } else if (report.summary) {
      // Summary format
      const summary = report.summary || {};
      const duration = report.duration || 'N/A';

      return `
        <div class="report-stats">
          <div class="report-stat">
            <span class="stat-label">Total Tests:</span>
            <span class="stat-value">${summary.totalTests || 0}</span>
          </div>
          <div class="report-stat">
            <span class="stat-label">Passed:</span>
            <span class="stat-value text-success">${summary.passed || 0}</span>
          </div>
          <div class="report-stat">
            <span class="stat-label">Failed:</span>
            <span class="stat-value text-danger">${summary.failed || 0}</span>
          </div>
          <div class="report-stat">
            <span class="stat-label">Skipped:</span>
            <span class="stat-value text-warning">${summary.skipped || 0}</span>
          </div>
          <div class="report-stat">
            <span class="stat-label">Duration:</span>
            <span class="stat-value">${duration || 'N/A'}</span>
          </div>
        </div>
      `;
    } else {
      // Generic object
      return `
        <pre>${JSON.stringify(report, null, 2)}</pre>
      `;
    }
  }

  /**
   * Format a timestamp for display
   * @param {string|Date} timestamp - The timestamp to format
   * @returns {string} - The formatted timestamp
   */
  formatTime(timestamp) {
    if (!timestamp) return 'N/A';

    try {
      const date = timestamp instanceof Date ? timestamp : new Date(timestamp);

      // Check if date is valid
      if (isNaN(date.getTime())) {
        return 'Invalid date';
      }

      // Format options
      const options = {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      };

      return date.toLocaleDateString(undefined, options);
    } catch (error) {
      console.error('Error formatting timestamp:', error);
      return 'Error';
    }
  }

  /**
   * Show loading indicator
   * @param {string} message - Loading message
   */
  showLoading(message) {
    // This is a placeholder - implement actual UI in your dashboard
    console.log('Loading:', message);

    // Example implementation:
    const loading = document.getElementById('loading-indicator');
    if (loading) {
      const messageElement = loading.querySelector('.loading-message');
      if (messageElement) {
        messageElement.textContent = message;
      }
      loading.style.display = 'block';
    }
  }

  /**
   * Hide loading indicator
   */
  hideLoading() {
    // This is a placeholder - implement actual UI in your dashboard
    console.log('Loading complete');

    // Example implementation:
    const loading = document.getElementById('loading-indicator');
    if (loading) {
      loading.style.display = 'none';
    }
  }

  /**
   * Show success message
   * @param {string} message - Success message
   */
  showSuccess(message) {
    // This is a placeholder - implement actual UI in your dashboard
    console.log('Success:', message);

    // Example implementation:
    this.showNotification('success', message);
  }

  /**
   * Show error message
   * @param {string} message - Error message
   */
  showError(message) {
    // This is a placeholder - implement actual UI in your dashboard
    console.error('Error:', message);

    // Example implementation:
    this.showNotification('error', message);
  }

  /**
   * Show notification
   * @param {string} type - Notification type (success, error, info)
   * @param {string} message - Notification message
   */
  showNotification(type, message) {
    // Example implementation:
    const container = document.getElementById('notification-container');
    if (container) {
      const notification = document.createElement('div');
      notification.className = `notification ${type}`;
      notification.innerHTML = `
        <span class="notification-message">${message}</span>
        <button class="notification-close">&times;</button>
      `;

      // Add close button handler
      const closeButton = notification.querySelector('.notification-close');
      if (closeButton) {
        closeButton.addEventListener('click', () => {
          notification.remove();
        });
      }

      // Auto-remove after 5 seconds
      setTimeout(() => {
        notification.classList.add('fade-out');
        setTimeout(() => {
          notification.remove();
        }, 500);
      }, 5000);

      container.appendChild(notification);
    }
  }

  /**
   * Set up event listeners for predefined test suites and custom suite builder
   */
  setupEventListeners() {
    console.log('Setting up event listeners for dashboard components');

    // Event listeners for predefined suites (Example)
    const predefinedSuitesContainer = document.getElementById('predefined-suites-container');
    if (predefinedSuitesContainer) {
      predefinedSuitesContainer.addEventListener('click', (event) => {
        const button = event.target.closest('[data-suite-id]');
        if (button) {
          const suiteId = button.dataset.suiteId;
          const suiteName = button.dataset.suiteName || `Suite ${suiteId}`;
          if (suiteId) {
            console.log(`Run button clicked for suite: ${suiteName} (${suiteId})`);
            this.runTestSuite(suiteId, { name: suiteName });
          }
        }
      });
    }

    // Add listener for the custom test case run button
    const customRunButton = document.getElementById('run-custom-tc-button');
    const customTcInput = document.getElementById('custom-tc-id-input');

    console.log('Custom test case elements:', {
      buttonFound: !!customRunButton,
      inputFound: !!customTcInput
    });

    // First remove any existing listeners to avoid duplicates
    if (customRunButton) {
      const newButton = customRunButton.cloneNode(true);
      customRunButton.parentNode.replaceChild(newButton, customRunButton);

      // Get the fresh reference
      const freshButton = document.getElementById('run-custom-tc-button');

      freshButton.addEventListener('click', () => {
        // Get the input element again to ensure we have the current reference
        const input = document.getElementById('custom-tc-id-input');

        // Log the input element and its value for debugging
        console.log('Input element:', input);
        console.log('Input value when button clicked:', input ? input.value : 'Input not found');

        if (input) {
          const tcIdValue = input.value.trim();
          console.log(`Parsed test case ID value: "${tcIdValue}"`);

          if (tcIdValue) {
            const tcId = tcIdValue; // Use as string to avoid parsing issues
            console.log(`Attempting to run custom test case ID: ${tcId}`);

            // Call the existing runTestCase method which handles API call and notifications
            this.runTestCase(tcId);

            // Clear the input field after clicking
            input.value = '';
          } else {
            console.warn('Empty input value detected');
            window.showNotification('Input Required', 'Please enter a Test Case ID.', 'warning');
          }
        } else {
          console.error('Could not find input element when button was clicked');
          window.showNotification('Error', 'Could not find the Test Case ID input field.', 'error');
        }
      });

      console.log('Event listener attached to run button');
    } else {
      console.warn('Custom test case run button not found.');
    }

    // Also attach the event listener directly to the input field for Enter key
    if (customTcInput) {
      customTcInput.addEventListener('keyup', (event) => {
        if (event.key === 'Enter') {
          event.preventDefault();
          const tcIdValue = customTcInput.value.trim();
          if (tcIdValue) {
            console.log(`Enter key pressed - running test case ID: ${tcIdValue}`);
            this.runTestCase(tcIdValue);
            customTcInput.value = '';
          } else {
            window.showNotification('Input Required', 'Please enter a Test Case ID.', 'warning');
          }
        }
      });

      console.log('Enter key event listener attached to input field');
    }
  }

  /**
   * Create and run a custom test suite from selected test cases
   */
  async createAndRunCustomSuite() {
    // Get custom suite name
    const suiteName = document.getElementById('custom-suite-name').value.trim() || 'Custom Test Suite';

    // Get selected test cases
    const selectedCheckboxes = document.querySelectorAll('#available-testcases input[type="checkbox"]:checked');

    if (!selectedCheckboxes || selectedCheckboxes.length === 0) {
      this.showError('Please select at least one test case for your custom suite.');
      return;
    }

    const selectedTestCases = Array.from(selectedCheckboxes).map(checkbox => parseInt(checkbox.value, 10));

    try {
      // Show loading indicator
      this.showLoading(`Creating and running ${suiteName}...`);

      // Create a dynamic test suite request
      const params = {
        name: suiteName,
        description: `Custom test suite created on ${new Date().toLocaleString()}`,
        test_cases: selectedTestCases.map(tcId => ({ id: tcId }))
      };

      // Run the dynamic test suite
      const tsnId = await this.apiService.runDynamicTestSuite(params);

      if (tsnId) {
        // Add to active tests
        this.activeTests.set(tsnId, {
          status: 'running',
          type: 'custom',
          startTime: new Date(),
          name: suiteName,
          progress: 0,
          messages: []
        });

        // Show success message
        this.showSuccess(`${suiteName} started successfully. Run ID: ${tsnId}`);

        // Update the UI
        this.renderActiveTests();

        // Close the custom suite modal
        const modal = document.getElementById('custom-suite-modal');
        if (modal) {
          modal.style.display = 'none';
        }

        // Clear selected checkboxes for next time
        selectedCheckboxes.forEach(checkbox => {
          checkbox.checked = false;
        });

        // Clear suite name for next time
        const nameInput = document.getElementById('custom-suite-name');
        if (nameInput) {
          nameInput.value = '';
        }
      } else {
        this.showError('Failed to start custom test suite. Please try again.');
      }
    } catch (error) {
      console.error('Error running custom suite:', error);
      this.showError(`Failed to run custom test suite: ${error.message || 'Unknown error'}`);
    } finally {
      // Hide loading indicator
      this.hideLoading();
    }
  }

  /**
   * Run a predefined test suite from the API
   * @param {string} suiteId - The identifier for the predefined suite
   * @returns {Promise<string|null>} The test session ID if successful
   */
  async runPredefinedSuite(suiteId) {
    try {
      // Show loading indicator
      this.showLoading(`Starting test suite ${suiteId}...`);

      // Get the suite details from the API
      const response = await this.apiService.getRequest(`/local/test-suites/${suiteId}`);

      if (!response.success || !response.data) {
        throw new Error(`Failed to get details for test suite ${suiteId}`);
      }

      const suite = response.data;

      // Create test session
      const sessionId = await this.apiService.createTestSession({
        test_type: suite.name || `Suite ${suiteId}`,
        environment: 'qa02',
        description: suite.comments || `Test suite ${suiteId}`
      });

      // Run the test suite
      const tsnId = await this.apiService.runTestSuite(suiteId, {
        name: suite.name || `Suite ${suiteId}`,
        session_id: sessionId
      });

      if (tsnId) {
        console.log(`Successfully started suite ${suite.name} with session ID ${tsnId}`, {
          suite_id: suiteId,
          session_id: tsnId
        });

        // Add to active tests with proper property names
        this.activeTests.set(tsnId, {
          tsn_id: tsnId,
          ts_id: suiteId, // Store the suite ID properly
          tc_id: suite.testCases[0],
          testName: suite.name, // Use consistent naming for the test name
          suiteName: suite.name, // Add explicit suite name for predefined suites
          status: 'running',
          sessionId: sessionId,
          startTime: new Date(),
          user: this.apiService.credentials.uid || 'You',
          progress: 0,
          messages: [],
          // Store test cases array for progress tracking
          testCases: suite.testCases,
          totalTests: suite.testCases.length
        });

        // Show success message
        this.showSuccess(`${suite.name} started successfully. Run ID: ${tsnId}`);

        // Update the UI
        this.renderActiveTests();

        // Start monitoring
        this.monitorTestSession(sessionId, tsnId);

        return tsnId;
      } else {
        throw new Error('Could not get test run ID');
      }
    } catch (error) {
      console.error('Error running predefined suite:', error);
      this.showError(`Failed to run ${suiteId} suite: ${error.message}`);
      return null;
    } finally {
      this.hideLoading();
    }
  }

  /**
   * Monitor test session status
   * @param {string} sessionId - Session ID
   * @param {number} tsnId - Test suite run ID
   */
  async monitorTestSession(sessionId, tsnId) {
    const pollInterval = 5000; // 5 seconds
    const maxAttempts = 120; // 10 minutes maximum

    let attempts = 0;
    const poll = async () => {
      try {
        // Get session status
        const sessionStatus = await this.apiService.getTestSessionStatus(sessionId);

        // Get query history
        const queryHistory = await this.apiService.getInputQueryHistory(sessionId);

        // Update active test
        const test = this.activeTests.get(tsnId);
        if (test) {
          test.status = sessionStatus.status;
          test.progress = sessionStatus.progress;
          test.messages = sessionStatus.messages;
          test.queryHistory = queryHistory;

          // Update UI
          this.updateTestStatusInUI(tsnId, test);

          // Check if test is complete
          if (sessionStatus.status === 'completed' || sessionStatus.status === 'failed') {
            // Get final report
            const report = await this.apiService.getTestSessionReport(sessionId);
            this.showReport(tsnId, report);

            // Remove from active tests after delay
            setTimeout(() => {
              if (this.activeTests.has(tsnId)) {
                console.log(`Removing completed test ${tsnId} from active tests`);
                this.activeTests.delete(tsnId);
                this.renderActiveTests();
              }
            }, 5000);

            return;
          }
        }

        // Continue polling if not complete
        attempts++;
        if (attempts < maxAttempts) {
          setTimeout(poll, pollInterval);
        } else {
          this.showError('Test session monitoring timed out');
        }
      } catch (error) {
        console.error('Error monitoring test session:', error);
        this.showError('Failed to monitor test session');
      }
    };

    poll();
  }

  /**
   * Update test status in UI
   * @param {number} tsnId - Test suite run ID
   * @param {Object} test - Test status object
   */
  updateTestStatusInUI(tsnId, test) {
    const testCard = document.querySelector(`[data-tsn-id="${tsnId}"]`);
    if (!testCard) return;

    // Update status class
    testCard.className = `test-card ${test.status}`;

    // Update status text
    const statusText = testCard.querySelector('.status-text');
    if (statusText) {
      statusText.textContent = test.status.charAt(0).toUpperCase() + test.status.slice(1);
    }

    // Update progress
    const progressBar = testCard.querySelector('.progress-bar');
    if (progressBar) {
      progressBar.style.width = `${test.progress}%`;
      progressBar.textContent = `${test.progress}%`;
    }

    // Update messages
    const messagesContainer = testCard.querySelector('.messages');
    if (messagesContainer) {
      messagesContainer.innerHTML = test.messages.map(msg =>
        `<div class="message ${msg.type}">${msg.text}</div>`
      ).join('');
    }

    // Update query history
    const queryHistory = testCard.querySelector('.query-history');
    if (queryHistory && test.queryHistory) {
      queryHistory.innerHTML = `
        <h4>Recent Queries</h4>
        <ul>
          ${test.queryHistory.map(query => `
            <li>
              <span class="query-status ${query.status}">${query.status}</span>
              <span class="query-time">${new Date(query.timestamp).toLocaleTimeString()}</span>
              <div class="query-text">${query.query}</div>
            </li>
          `).join('')}
        </ul>
      `;
    }

    // Update buttons
    const stopButton = testCard.querySelector('.stop-button');
    if (stopButton) {
      stopButton.style.display = test.status === 'running' ? 'block' : 'none';
    }
  }

  /**
   * Show the login form modal
   */
  showLoginForm() {
    console.log('Showing login form modal');
    window.showLoginModal();
  }

  /**
   * Handle successful login
   * @param {string} username - The logged in username
   */
  handleSuccessfulLogin(username) {
    console.log(`Handling successful login for user: ${username}`);
    window.handleSuccessfulLogin(username);
  }

  /**
   * Handle logout
   */
  handleLogout() {
    console.log('Handling logout');
    window.handleLogout();
  }

  /**
   * Process the data for a test and ensure all required fields are set
   * @param {Object} testData - The test data to process
   * @returns {Object} - The processed test data
   */
  processTestData(testData) {
    // Create a copy to avoid modifying the original
    const test = { ...testData };
    
    // Check if this is a test suite by examining its name and type
    const isSuiteName = test.name && (
      test.name.toLowerCase().includes('suite') || 
      test.name.toLowerCase().includes('smoke test') ||
      test.name.toLowerCase().includes('heartbeat') ||
      test.name.toLowerCase().includes('pe2.1')
    );
    
    // If it looks like a test suite but doesn't have ts_id, set it
    if ((isSuiteName || test.type === 'Test Suite') && !test.ts_id) {
      // If it's a PE2.1 Smoke Test, use 332 as the ts_id
      if (test.name && test.name.includes('PE2.1 Smoke Test')) {
        test.ts_id = 332;
      }
      // If it's a PE2.1 Heartbeat Test, use 333 as the ts_id
      else if (test.name && test.name.includes('PE2.1 Heartbeat')) {
        test.ts_id = 333;
      }
      // Otherwise use a generic fallback
      else {
        test.ts_id = 332;
      }
    }
    
    // Make sure we have a valid tsn_id (Test Session Number)
    if (!test.tsn_id && test.id) {
      test.tsn_id = test.id;
    }
    
    // Set type based on name if not present
    if (!test.type) {
      test.type = isSuiteName ? 'Test Suite' : 'Test Case';
    }
    
    return test;
  }

  /**
   * Format user email into a display name
   * @param {string} email - User email address
   * @returns {string} - Formatted display name
   */
  formatUserEmail(email) {
    if (!email) return 'Unknown';
    
    try {
      // Parse email in the format <first_name>.<last_name>@<domain>
      const namePart = email.split('@')[0];
      if (!namePart) return email;
      
      const nameParts = namePart.split('.');
      if (nameParts.length < 2) return email;
      
      // Capitalize first and last name
      const firstName = nameParts[0].charAt(0).toUpperCase() + nameParts[0].slice(1);
      const lastName = nameParts[1].charAt(0).toUpperCase() + nameParts[1].slice(1);
      
      return `${firstName} ${lastName}`;
    } catch (error) {
      console.error('Error formatting email:', error);
      return email;
    }
  }

  /**
   * Show test details in modal
   * @param {string} tsnId - Test session ID
   * @param {Object} testData - Test data
   */
  showTestDetails(tsnId, testData) {
    console.log(`Showing test details for session ${tsnId}:`, testData);
    
    // Get the modal element
    const modal = document.getElementById('report-modal');
    const content = document.getElementById('report-content');
    
    if (!modal || !content) {
      console.error('Report modal elements not found');
      return;
    }
    
    // Format the report content
    let reportHtml = '';
    
    if (testData.report) {
      // If we have a raw report, display it directly
      reportHtml = `
        <div class="ms-report-header">
          <h3>${testData.test_name || 'Test Report'}</h3>
          <div class="ms-report-meta">
            <div><strong>ID:</strong> ${tsnId}</div>
            <div><strong>Test ID:</strong> ${testData.tc_id || testData.ts_id || 'N/A'}</div>
            <div><strong>Initiator:</strong> ${this.formatUserEmail(testData.uid)}</div>
            <div><strong>Status:</strong> <span class="status-${testData.status?.toLowerCase() || 'unknown'}">${testData.status || 'Unknown'}</span></div>
            <div><strong>Started:</strong> ${testData.start_ts || 'N/A'}</div>
            <div><strong>Completed:</strong> ${testData.end_ts || 'N/A'}</div>
            <div><strong>Pass Rate:</strong> ${testData.passed_cases || 0}/${(testData.passed_cases || 0) + (testData.failed_cases || 0)}</div>
          </div>
        </div>
        <div class="ms-report-content">
          ${testData.report}
        </div>
      `;
    } else {
      // Create a basic report from the available data
      reportHtml = `
        <div class="ms-report-header">
          <h3>${testData.test_name || 'Test Report'}</h3>
          <div class="ms-report-meta">
            <div><strong>ID:</strong> ${tsnId}</div>
            <div><strong>Test ID:</strong> ${testData.tc_id || testData.ts_id || 'N/A'}</div>
            <div><strong>Initiator:</strong> ${this.formatUserEmail(testData.uid)}</div>
            <div><strong>Status:</strong> <span class="status-${testData.status?.toLowerCase() || 'unknown'}">${testData.status || 'Unknown'}</span></div>
            <div><strong>Started:</strong> ${testData.start_ts || testData.start_time || 'N/A'}</div>
            <div><strong>Completed:</strong> ${testData.end_ts || testData.end_time || 'N/A'}</div>
          </div>
        </div>
        <div class="ms-report-summary">
          <p>Detailed report not available.</p>
        </div>
      `;
    }
    
    // Set modal content
    content.innerHTML = reportHtml;
    
    // Add close functionality
    const closeButtons = modal.querySelectorAll('.ms-modal-close');
    closeButtons.forEach(btn => {
      btn.onclick = () => {
        modal.style.display = 'none';
      };
    });
    
    // Show modal
    modal.style.display = 'block';
    
    // Close modal when clicking outside
    window.onclick = (event) => {
      if (event.target === modal) {
        modal.style.display = 'none';
      }
    };
  }

  /**
   * Update the recent runs table with data
   * @param {Array} recentRuns - The recent runs data
   */
  updateRecentRunsTable(recentRuns = []) {
    console.log('Updating recent runs table with data:', recentRuns);

    try {
      // Find the table - try multiple selector strategies
      let recentRunsTable = document.getElementById('reports-table');

      if (!recentRunsTable) {
        // Strategy 1: Look for table with Test ID header
        const tables = document.querySelectorAll('table');
        for (const table of tables) {
          const headers = table.querySelectorAll('th');
          for (const header of headers) {
            if (header.textContent.includes('Test ID')) {
              recentRunsTable = table;
              console.log('Found recent runs table using Test ID header');
              break;
            }
          }
          if (recentRunsTable) break;
        }
      }

      // Strategy 2: Look for table with specific class or id
      if (!recentRunsTable) {
        recentRunsTable = document.querySelector('.recent-runs-table') ||
                          document.querySelector('#recent-runs-table');
        if (recentRunsTable) {
          console.log('Found recent runs table using class/id selector');
        }
      }

      // Strategy 3: Look for any table within the dashboard content area
      if (!recentRunsTable) {
        const dashboardContent = document.querySelector('.dashboard-content') ||
                                document.querySelector('#dashboard-content');
        if (dashboardContent) {
          recentRunsTable = dashboardContent.querySelector('table');
          if (recentRunsTable) {
            console.log('Found recent runs table within dashboard content');
          }
        }
      }

      // Strategy 4: Last resort - take the first table in the document
      if (!recentRunsTable) {
        recentRunsTable = document.querySelector('table');
        if (recentRunsTable) {
          console.log('Using first table as recent runs table (fallback)');
        }
      }

      if (!recentRunsTable) {
        console.error('Recent runs table not found');
        return;
      }

      // Get or create tbody
      let tableBody = recentRunsTable.querySelector('tbody');
      if (!tableBody) {
        tableBody = document.createElement('tbody');
        recentRunsTable.appendChild(tableBody);
      }

      // Clear existing rows
      tableBody.innerHTML = '';

      // Add a "no data" row if no recent runs
      if (!recentRuns || recentRuns.length === 0) {
        const noDataRow = document.createElement('tr');
        const headerRow = recentRunsTable.querySelector('thead tr');
        const columns = headerRow ? headerRow.querySelectorAll('th').length : 10;

        const noDataCell = document.createElement('td');
        noDataCell.setAttribute('colspan', columns);
        noDataCell.textContent = 'No recent test runs found';
        noDataCell.style.textAlign = 'center';
        noDataCell.style.padding = '10px';

        noDataRow.appendChild(noDataCell);
        tableBody.appendChild(noDataRow);
        return;
      }

      // Store the full test data for use in the details view
      this.testDetailsCache = {};

      // Add new rows
      recentRuns.forEach(run => {
        const row = document.createElement('tr');
        
        // Cache the full test data for the details view
        const tsnId = run.tsn_id || run.id || '';
        this.testDetailsCache[tsnId] = run;

        // Add appropriate class based on status
        if (run.status && run.status.toLowerCase() === 'failed') {
          row.classList.add('table-danger');
        } else if (run.status && run.status.toLowerCase() === 'passed') {
          row.classList.add('table-success');
        }

        // Format the dates
        let startTime = 'N/A';
        let endTime = 'N/A';
        try {
          startTime = this.formatTime(run.start_time || run.created_at || run.timestamp);
          endTime = this.formatTime(run.end_time || '');
        } catch (e) {
          console.error('Error formatting date:', e);
        }

        // Get the test ID and name
        const testId = run.tc_id || run.ts_id || run.test_id || '';
        const testName = run.test_name || run.name || `Test ${testId}`;
        
        // Format user email to display name
        const userEmail = run.uid || run.user_id || '';
        const userName = this.formatUserEmail(userEmail);

        // Determine passed/failed count
        const passedCount = run.passed_cases || 0;
        const failedCount = run.failed_cases || 0;

        row.innerHTML = `
          <td>${tsnId}</td>
          <td>${testName}</td>
          <td>${testId}</td>
          <td>${run.status || 'Unknown'}</td>
          <td>${startTime}</td>
          <td>${endTime}</td>
          <td title="${userEmail}">${userName}</td>
          <td class="text-success">${passedCount}</td>
          <td class="text-danger">${failedCount}</td>
          <td>
            <button class="ms-Button ms-Button--primary test-details-btn" data-tsn-id="${tsnId}">
              <span class="ms-Button-label">Details</span>
            </button>
          </td>
        `;

        tableBody.appendChild(row);
      });

      // Attach event listeners to the details buttons
      const detailsButtons = recentRunsTable.querySelectorAll('.test-details-btn');
      detailsButtons.forEach(button => {
        button.addEventListener('click', (event) => {
          const tsnId = event.currentTarget.getAttribute('data-tsn-id');
          if (tsnId && this.testDetailsCache[tsnId]) {
            this.showTestDetails(tsnId, this.testDetailsCache[tsnId]);
          } else {
            console.error(`Test data not found for session ${tsnId}`);
          }
        });
      });

      console.log('Recent runs table updated successfully');
    } catch (error) {
      console.error('Error updating recent runs table:', error);
    }
  }
}

// Initialize the API integration when the document is loaded
document.addEventListener('DOMContentLoaded', function() {
  console.log('Initializing dashboard API integration...');
  window.dashboardApiIntegration = new DashboardApiIntegration();
  window.dashboardApiIntegration.initialize();
});