/**
 * SmartTest API Service
 *
 * This service provides the real API integration for the SmartTest application,
 * interacting with the backend services to run and monitor tests.
 */

class ApiService {
  constructor() {
    // Use relative URL that will resolve correctly with the new server architecture
    // This works because all requests will be served from the same origin
    this.baseUrl = '/api';

    // Log the base URL for debugging
    console.log('API Service initialized with baseUrl:', this.baseUrl);

    // Setup API endpoints
    this.endpoints = {
      caseRunner: '/case-runner',         // -> /api/case-runner
      testStatus: '/test-status',         // -> /api/test-status
      testReports: '/test-reports',       // -> /api/test-reports
      testSuites: '/local/test-suites',   // -> /local/test-suites
      testCases: '/local/test-cases',     // -> /local/test-cases
      stopTest: '/stop-test',             // -> /api/stop-test
      rerunFailed: '/rerun-failed',       // -> /api/rerun-failed
      activeTests: '/local/active-tests', // -> /local/active-tests
      recentRuns: '/local/recent-runs',   // -> /local/recent-runs
    };

    // Default credentials
    this.credentials = { uid: '', password: '' };

    // Load credentials from session storage
    this.loadCredentials();

    // Debug log the endpoints
    console.log('API endpoints initialized:', this.endpoints);
  }

  /**
   * Set API credentials
   * @param {string} username - Username
   * @param {string} password - Password
   */
  setCredentials(username, password) {
    this.credentials = { uid: username, password: password };

    // Save to session storage
    sessionStorage.setItem('smarttest_uid', username);
    sessionStorage.setItem('smarttest_pwd', password);

    console.log('API credentials set for user:', username);
    return true;
  }

  /**
   * Load credentials from session storage or environment
   * @returns {boolean} - Whether credentials were successfully loaded
   */
  loadCredentials() {
    try {
      // Try to load from session storage first
      const uid = sessionStorage.getItem('smarttest_uid');
      const password = sessionStorage.getItem('smarttest_pwd');

      if (uid && password) {
        this.credentials = { uid, password };
        console.log(`Credentials loaded for user: ${uid}`);
        return true;
      }

      console.log('No valid credentials found, user needs to log in');
      return false;
    } catch (error) {
      console.error('Error loading credentials:', error);
      return false;
    }
  }

  /**
   * Get authentication parameters for API requests
   * @returns {Object} - Authentication parameters
   */
  getAuthParams() {
    return {
      uid: this.credentials.uid,
      password: this.credentials.password
    };
  }

  /**
   * Make a GET request to the API
   * @param {string} endpoint - The endpoint to call
   * @param {Object} params - Additional parameters
   * @returns {Promise<any>} - The response from the API
   */
  async getRequest(endpoint, params = {}) {
    try {
      // Determine if this is a local endpoint or an API endpoint
      // Local endpoints start with '/local/' and should not be prefixed with baseUrl
      const isLocalEndpoint = endpoint.startsWith('/local/');
      
      // Build complete URL: For local endpoints, use as-is; for API endpoints, prepend baseUrl
      const url = isLocalEndpoint ? endpoint : this.baseUrl + endpoint;

      console.log(`Making GET request to: ${url}`);

      // Add authentication parameters and any additional parameters
      const allParams = {
        ...params,
        uid: this.credentials.uid,
        password: this.credentials.password
      };

      // Build query string
      const queryString = Object.entries(allParams)
        .map(([key, value]) => `${encodeURIComponent(key)}=${encodeURIComponent(value)}`)
        .join('&');

      // Make the request
      const response = await fetch(`${url}?${queryString}`);

      // Handle non-200 responses
      if (!response.ok) {
        const errorText = await response.text();
        console.error(`API error (${response.status}): ${errorText}`);
        throw new Error(`API request failed with status ${response.status}`);
      }

      // Parse the response
      return await response.json();
    } catch (error) {
      console.error(`Error making GET request to ${endpoint}:`, error);
      throw error;
    }
  }

  /**
   * Make a POST request to the API
   * @param {string} endpoint - The endpoint to call
   * @param {Object} params - The parameters to send
   * @returns {Promise<any>} - The response from the API
   */
  async postRequest(endpoint, params = {}) {
    try {
      const url = this.baseUrl + endpoint;

      console.log(`Making POST request to: ${url}`);

      // Add authentication parameters
      const requestData = {
        ...params,
        uid: this.credentials.uid,
        password: this.credentials.password
      };

      // Make the request
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestData)
      });

      // Handle non-200 responses
      if (!response.ok) {
        const errorText = await response.text();
        console.error(`API error (${response.status}): ${errorText}`);

        // Try to parse error as JSON if possible
        try {
          const errorJson = JSON.parse(errorText);
          if (errorJson && errorJson.message) {
            throw new Error(errorJson.message);
          }
        } catch (parseError) {
          // If can't parse JSON, just use the text
        }

        throw new Error(`API request failed with status ${response.status}`);
      }

      // Parse JSON response
      const data = await response.json();
      return data;
    } catch (error) {
      console.error(`Error making POST request to ${endpoint}:`, error);
      throw error;
    }
  }

  /**
   * Run a specific test case by ID
   * @param {string} tcId - Test case ID to run
   * @param {Object} params - Additional parameters to pass to the test case runner
   * @returns {Promise<Object>} - Response from the API
   */
  async runTestCase(tcId, params = {}) {
    try {
      // Add required parameters for the test case runner
      const testParams = {
        tc_id: tcId,
        user_id: this.credentials.uid,
        username: this.credentials.uid,
        ...ApiService.DEFAULT_TEST_PARAMS,
        ...params
      };

      const response = await this.postRequest(this.endpoints.caseRunner, testParams);

      if (response.success) {
        console.log(`Test case ${tcId} running with session ID: ${response.tsn_id}`);
        return response;
      } else {
        throw new Error(response.message || `Failed to run test case ${tcId}`);
      }
    } catch (error) {
      console.error(`Error running test case ${tcId}:`, error);
      throw error;
    }
  }

  /**
   * Run a test suite
   * @param {number} tsId - Test suite ID
   * @param {Object} params - Additional parameters
   * @returns {Promise<number>} - Test suite run ID
   */
  async runTestSuite(tsId, params = {}) {
    try {
      // Validate test suite ID
      if (!tsId) {
        throw new Error('Test suite ID is required');
      }

      // Add required parameters for the test suite runner
      const testParams = {
        ts_id: tsId,
        user_id: this.credentials.uid,
        username: this.credentials.uid,
        ...ApiService.DEFAULT_TEST_PARAMS,
        ...params
      };

      console.log(`Running test suite ${tsId} with params:`, testParams);

      const response = await this.postRequest(this.endpoints.caseRunner, testParams);

      if (response && response.tsn_id) {
        console.log(`Test suite ${tsId} running with session ID: ${response.tsn_id}`);
        return response.tsn_id;
      } else {
        throw new Error(response.message || `Failed to run test suite ${tsId}`);
      }
    } catch (error) {
      console.error(`Error running test suite ${tsId}:`, error);
      throw error;
    }
  }

  /**
   * Get test status
   * @param {number} tsnId - Test suite run ID
   * @returns {Promise<Object>} - Test status data
   */
  async getTestStatus(tsnId) {
    try {
      return await this.getRequest(this.endpoints.testStatus, { tsn_id: tsnId });
    } catch (error) {
      console.error(`Error getting test status for ${tsnId}:`, error);
      throw error;
    }
  }

  /**
   * Get report summary for a test run
   * @param {number} tsnId - Test suite run ID
   * @returns {Promise<Object>} - Test report data
   */
  async getReportSummary(tsnId) {
    try {
      // Use the correct URL pattern for the test reports endpoint
      // The server expects /api/test-reports/:tsn_id/summary format
      return await this.getRequest(`${this.endpoints.testReports}/${tsnId}/summary`);
    } catch (error) {
      console.error(`Error getting report summary for ${tsnId}:`, error);
      throw error;
    }
  }

  /**
   * Get detailed report for a test run
   * @param {number} tsnId - Test suite run ID
   * @returns {Promise<Object>} - Test report data
   */
  async getTestReport(tsnId) {
    try {
      // Use the correct URL pattern for the test reports endpoint
      // The server expects /api/test-reports/:tsn_id format
      return await this.getRequest(`${this.endpoints.testReports}/${tsnId}`);
    } catch (error) {
      console.error(`Error getting report for ${tsnId}:`, error);
      throw error;
    }
  }

  /**
   * Get available test suites
   * @returns {Promise<Array>} - List of test suites
   */
  async getTestSuites() {
    try {
      const response = await this.getRequest(this.endpoints.testSuites);
      // Only handle the new refactored API response format
      return response.success && Array.isArray(response.data) ? response.data : [];
    } catch (error) {
      console.error('Error getting test suites:', error);
      throw error;
    }
  }

  /**
   * Get available test cases
   * @returns {Promise<Array>} - List of test cases
   */
  async getTestCases() {
    try {
      const response = await this.getRequest(this.endpoints.testCases);
      // Only handle the new refactored API response format
      return response.success && Array.isArray(response.data) ? response.data : [];
    } catch (error) {
      console.error('Error getting test cases:', error);
      throw error;
    }
  }

  /**
   * Stop a running test
   * @param {number} tsnId - Test suite run ID
   * @returns {Promise<boolean>} - Success status
   */
  async stopTest(tsnId) {
    try {
      const response = await this.postRequest(this.endpoints.stopTest, { tsn_id: tsnId });
      return response.success === true;
    } catch (error) {
      console.error(`Error stopping test ${tsnId}:`, error);
      throw error;
    }
  }

  /**
   * Rerun failed tests from a previous test run
   * @param {number} tsnId - Original test suite run ID
   * @param {Object} params - Additional parameters
   * @returns {Promise<number>} - New test suite run ID
   */
  async rerunFailedTests(tsnId, params = {}) {
    try {
      const response = await this.postRequest(this.endpoints.rerunFailed, {
        tsn_id: tsnId,
        user_id: this.credentials.uid,
        username: this.credentials.uid,
        ...params
      });

      if (response && response.tsn_id) {
        return response.tsn_id;
      } else {
        throw new Error('Failed to get test suite run ID for rerun');
      }
    } catch (error) {
      console.error(`Error rerunning failed tests from ${tsnId}:`, error);
      throw error;
    }
  }

  /**
   * Get active tests
   * @returns {Promise<Array>} - List of active tests
   */
  async getActiveTests() {
    try {
      const response = await this.getRequest(this.endpoints.activeTests);
      // Only handle the new refactored API response format
      return response.success && Array.isArray(response.data) ? response.data : [];
    } catch (error) {
      console.error('Error getting active tests:', error);
      throw error;
    }
  }

  /**
   * Get test results data for dashboard display
   * @returns {Promise<Object>} - Dashboard data
   */
  async getDashboardData() {
    try {
      // Query for active test data
      const activeTestsData = await this.getActiveTests();

      // Get recent test runs - we'll get this from SQL in real implementation
      // For now, construct from active tests data
      const recentRuns = activeTestsData.map(test => ({
        id: test.tsn_id,
        type: 'Test Case',
        environment: 'QA02',
        status: test.status || 'running',
        startTime: test.latest_activity || new Date().toISOString(),
        duration: 0
      }));

      // Count status totals
      const total = activeTestsData.length;
      const successful = activeTestsData.filter(test => test.outcome === 'P').length;
      const failed = activeTestsData.filter(test => test.outcome === 'F').length;
      const running = activeTestsData.filter(test => !test.outcome).length;

      return {
        summary: { total, successful, failed, running },
        recent: recentRuns,
        environment: 'QA02'
      };
    } catch (error) {
      console.error('Error getting dashboard data:', error);
      throw error;
    }
  }

  /**
   * Get test reports for the reports page
   * @param {Object} params - Optional parameters like timeRange
   * @returns {Promise<Array>} - List of test reports
   */
  async getTestReports(params = {}) {
    try {
      const response = await this.getRequest(this.endpoints.testReports, params);
      // Only handle the new refactored API response format
      return response.success && Array.isArray(response.data) ? response.data : [];
    } catch (error) {
      console.error('Error getting test reports:', error);
      throw error;
    }
  }

  // Default test parameters to use with API requests
  static get DEFAULT_TEST_PARAMS() {
    return {
      environment: 'qa02',
      shell_host: 'jps-qa10-app01',
      file_path: '/home/<USER>/',
      operatorConfigs: 'operatorNameConfigs',
      kafka_server: 'kafka-qa-a0.lab.wagerworks.com',
      dataCenter: 'GU',
      rgs_env: 'qa02',
      old_version: '0',
      networkType1: 'multi-site',
      networkType2: 'multi-site',
      sign: '-',
      rate_src: 'local'
    };
  }
}

// Create global API service instance
window.apiService = new ApiService();

// Export for module usage (if needed)
if (typeof module !== 'undefined' && typeof module.exports !== 'undefined') {
  module.exports = ApiService;
}
