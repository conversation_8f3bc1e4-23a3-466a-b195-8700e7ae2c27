/**
 * External API Service for SmartTest Reports
 * 
 * This service provides direct integration with the external API endpoints on port 9080
 * for retrieving test reports and details using cookie-based authentication.
 */

class ExternalApiService {
  constructor() {
    // Base URL for the external API - use local proxy instead of direct connection
    this.baseUrl = '/api';
    
    // Session state
    this.jsessionId = null;
    this.jsessionExpiry = null;
    
    // <PERSON><PERSON> expires after 30 minutes on server, we'll use 25 minutes to be safe
    this.cookieExpiryTime = 25 * 60 * 1000;
    
    console.log('External API Service initialized with baseUrl:', this.baseUrl);
  }
  
  /**
   * Check if the current session is valid
   * @returns {boolean} - Whether the session is valid
   */
  isSessionValid() {
    if (!this.jsessionId || !this.jsessionExpiry) {
      return false;
    }
    
    return Date.now() < this.jsessionExpiry;
  }
  
  /**
   * Login to the external API and get a JSESSIONID cookie
   * @param {string} uid - User ID
   * @param {string} password - Password
   * @returns {Promise<string>} - JSESSIONID cookie value
   */
  async login(uid, password) {
    try {
      console.log(`[ExternalApiService] Attempting login to external API as ${uid}...`);
      
      // Create form data for login
      const formData = new URLSearchParams();
      formData.append('uid', uid);
      formData.append('password', password);
      
      // Log the request details (masking the password)
      console.log(`[ExternalApiService] Login request details:`, {
        url: `${this.baseUrl}/Login`,
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
          'Origin': 'http://mprts-qa02.lab.wagerworks.com:9080',
          'Referer': 'http://mprts-qa02.lab.wagerworks.com:9080/AutoRun/Login'
        },
        body: formData.toString().replace(/password=([^&]*)/, 'password=****')
      });
      
      // Make login request - IMPORTANT: Set redirect to 'follow' to handle 302 redirects
      const response = await fetch(`${this.baseUrl}/Login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
          'Origin': 'http://mprts-qa02.lab.wagerworks.com:9080',
          'Referer': 'http://mprts-qa02.lab.wagerworks.com:9080/AutoRun/Login'
        },
        body: formData,
        credentials: 'include',
        redirect: 'follow' // Follow redirects to get to the authenticated page
      });
      
      console.log(`[ExternalApiService] Login response status:`, response.status);
      console.log(`[ExternalApiService] Login response headers:`, {
        'content-type': response.headers.get('content-type'),
        'set-cookie': response.headers.get('set-cookie') ? 'Present (not shown for security)' : 'Not present'
      });
      
      // Check for successful response (either 200 OK or 302 Found)
      if (!response.ok && response.status !== 302) {
        console.error(`[ExternalApiService] Login failed with status ${response.status}`);
        throw new Error(`Login failed with status ${response.status}`);
      }
      
      // Read response text to verify login success
      const responseText = await response.text();
      
      // Log a preview of the response text (first 500 chars)
      console.log(`[ExternalApiService] Login response text preview:`, 
        responseText.substring(0, 500) + (responseText.length > 500 ? '...' : ''));
      
      // Check if we got redirected to the login page again (login failed)
      if (responseText.includes('<title>Login Page</title>') || 
          responseText.includes('Please enter your email account and password')) {
        console.error('[ExternalApiService] Login failed - redirected back to login page');
        throw new Error('Login failed - invalid credentials');
      }
      
      // More robust login verification - check for multiple success indicators
      const loginSuccessful = 
        responseText.includes(uid) || 
        responseText.includes('Welcome') || 
        responseText.includes('Logout') ||
        responseText.includes('successfully') ||
        (response.status === 200 && !responseText.includes('<title>Login Page</title>'));
      
      // Log which success indicators were found
      console.log(`[ExternalApiService] Login verification indicators:`, {
        containsUserId: responseText.includes(uid),
        containsWelcome: responseText.includes('Welcome'),
        containsLogout: responseText.includes('Logout'),
        containsSuccessfully: responseText.includes('successfully'),
        status200: response.status === 200,
        isNotLoginPage: !responseText.includes('<title>Login Page</title>'),
        overallResult: loginSuccessful
      });
      
      if (loginSuccessful) {
        console.log('[ExternalApiService] Login successful, verified login indicators in response');
        // Set session as valid for 25 minutes
        this.jsessionExpiry = Date.now() + this.cookieExpiryTime;
        return true;
      } else {
        console.error('[ExternalApiService] Login verification failed - no success indicators found in response');
        console.log('[ExternalApiService] Response status:', response.status);
        console.log('[ExternalApiService] Response text excerpt:', responseText.substring(0, 200) + '...');
        
        // Try to use the server-side login API instead
        console.log('[ExternalApiService] Attempting to use server-side login API...');
        
        // Make a request to the server-side login endpoint
        const serverLoginResponse = await fetch('/api/login-proxy', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({ uid, password }),
          credentials: 'include'
        });
        
        if (serverLoginResponse.ok) {
          console.log('[ExternalApiService] Server-side login successful');
          this.jsessionExpiry = Date.now() + this.cookieExpiryTime;
          return true;
        } else {
          throw new Error('Login verification failed');
        }
      }
    } catch (error) {
      console.error('[ExternalApiService] Error logging in to external API:', error);
      console.error('[ExternalApiService] Error name:', error.name);
      console.error('[ExternalApiService] Error message:', error.message);
      console.error('[ExternalApiService] Error stack:', error.stack);
      throw error;
    }
  }
  
  /**
   * Get a valid JSESSIONID, logging in if necessary
   * @param {string} uid - User ID
   * @param {string} password - Password
   * @returns {Promise<boolean>} - Whether a valid session exists or was created
   */
  async getValidSession(uid, password) {
    if (this.isSessionValid()) {
      return true;
    }
    
    return await this.login(uid, password);
  }
  
  /**
   * Make an authenticated request to the external API
   * @param {string} endpoint - API endpoint
   * @param {Object} params - Query parameters
   * @param {string} uid - User ID
   * @param {string} password - Password
   * @param {string} method - HTTP method (GET or POST)
   * @returns {Promise<Response>} - Fetch response
   */
  async makeAuthenticatedRequest(endpoint, params = {}, uid, password, method = 'GET') {
    try {
      // Ensure we have a valid session first
      await this.getValidSession(uid, password);
      
      // Build URL with query parameters
      const url = new URL(`${this.baseUrl}${endpoint}`);
      Object.entries(params).forEach(([key, value]) => {
        url.searchParams.append(key, value);
      });
      
      // Request options with credentials to include cookies
      const options = {
        method,
        headers: {
          'Referer': 'http://mprts-qa02.lab.wagerworks.com:9080/AutoRun',
          'Origin': 'http://mprts-qa02.lab.wagerworks.com:9080'
        },
        credentials: 'include' // Include cookies automatically
      };
      
      // Add body for POST requests
      if (method === 'POST') {
        const formData = new URLSearchParams();
        Object.entries(params).forEach(([key, value]) => {
          formData.append(key, value);
        });
        options.body = formData;
        options.headers['Content-Type'] = 'application/x-www-form-urlencoded; charset=UTF-8';
      }
      
      console.log(`Making ${method} request to ${url.toString()}`);
      
      // Make the request
      const response = await fetch(url.toString(), options);
      
      if (!response.ok) {
        throw new Error(`Request failed with status ${response.status}`);
      }
      
      return response;
    } catch (error) {
      console.error(`Error making authenticated request to ${endpoint}:`, error);
      console.error('Error name:', error.name);
      console.error('Error message:', error.message);
      console.error('Error stack:', error.stack);
      throw error;
    }
  }
  
  /**
   * Get report summary for a test session
   * @param {string} tsnId - Test session ID
   * @param {string} uid - User ID
   * @param {string} password - Password
   * @returns {Promise<Object>} - Parsed report summary data
   */
  async getReportSummary(tsnId, uid, password) {
    try {
      console.log(`Getting report summary for test session ${tsnId}...`);
      
      // Build the new endpoint URL using the REST-style pattern
      const endpoint = `/test-reports/${tsnId}/summary`;
      
      // Make authenticated request to the endpoint
      const response = await this.makeAuthenticatedRequest(endpoint, {}, uid, password);
      
      // Check if response is JSON
      const contentType = response.headers.get('content-type');
      if (contentType && contentType.includes('application/json')) {
        // Parse JSON response
        const data = await response.json();
        return data.summary || data;
      } else {
        // Parse HTML response
        const html = await response.text();
        return this.parseReportSummaryHtml(html, tsnId);
      }
    } catch (error) {
      console.error(`Error getting report summary for ${tsnId}:`, error);
      throw error;
    }
  }
  
  /**
   * Get report details for a test session
   * @param {string} tsnId - Test session ID
   * @param {number} index - Page number (default: 1)
   * @param {string} uid - User ID
   * @param {string} password - Password
   * @returns {Promise<Object>} - Parsed report details data
   */
  async getReportDetails(tsnId, index = 1, uid, password) {
    try {
      console.log(`Getting report details for test session ${tsnId}, page ${index}...`);
      
      // Build the new endpoint URL using the REST-style pattern
      const endpoint = `/test-reports/${tsnId}`;
      
      // Make authenticated request to the endpoint
      const response = await this.makeAuthenticatedRequest(endpoint, {
        page: index
      }, uid, password);
      
      // Check if response is JSON
      const contentType = response.headers.get('content-type');
      if (contentType && contentType.includes('application/json')) {
        // Parse JSON response
        const data = await response.json();
        return data.reports || data;
      } else {
        // Parse HTML response
        const html = await response.text();
        return this.parseReportDetailsHtml(html, tsnId);
      }
    } catch (error) {
      console.error(`Error getting report details for ${tsnId}:`, error);
      throw error;
    }
  }
  
  /**
   * Stop a running test session
   * @param {string} tsnId - Test session ID
   * @param {string} uid - User ID
   * @param {string} password - Password
   * @returns {Promise<boolean>} - Whether the stop was successful
   */
  async stopTestSession(tsnId, uid, password) {
    try {
      console.log(`Stopping test session ${tsnId}...`);
      
      // Make authenticated POST request to RemoveSession endpoint
      const response = await this.makeAuthenticatedRequest(
        '/RemoveSession',
        { tsn_id: tsnId },
        uid,
        password,
        'POST'
      );
      
      // Get text response
      const text = await response.text();
      
      // Check if the response is "Removed"
      const success = text.trim() === 'Removed';
      
      console.log(`Stop test session ${tsnId} ${success ? 'successful' : 'failed'}`);
      return success;
    } catch (error) {
      console.error(`Error stopping test session ${tsnId}:`, error);
      throw error;
    }
  }
  
  /**
   * Parse HTML from ReportSummary endpoint to extract report data
   * @param {string} html - HTML response from ReportSummary endpoint
   * @param {string} tsnId - Test session ID
   * @returns {Object} - Parsed report data
   */
  parseReportSummaryHtml(html, tsnId) {
    try {
      // Create a DOM parser
      const parser = new DOMParser();
      const doc = parser.parseFromString(html, 'text/html');
      
      // Extract overall status
      let status = 'Unknown';
      const statusSpan = doc.querySelector('span[style*="color:red"], span[style*="color:green"]');
      if (statusSpan) {
        status = statusSpan.textContent.trim();
      }
      
      // Extract dates more aggressively
      let startTime = null;
      let endTime = null;
      
      // First try to find through list items (most common format)
      const listItems = doc.querySelectorAll('ul li');
      listItems.forEach(item => {
        const text = item.textContent.trim();
        if (text.startsWith('Start Time:')) {
          startTime = text.replace('Start Time:', '').trim();
        } else if (text.startsWith('End Time:')) {
          endTime = text.replace('End Time:', '').trim();
        }
      });
      
      // If not found through list items, look for it in any text node
      if (!startTime || !endTime) {
        const allTextNodes = Array.from(doc.querySelectorAll('*')).filter(el => 
          el.textContent.includes('Start Time:') || el.textContent.includes('End Time:')
        );
        
        allTextNodes.forEach(node => {
          const text = node.textContent.trim();
          
          // Check for Start Time
          const startTimeMatch = text.match(/Start Time:\s*([\d-]+\s+[\d:]+)/);
          if (startTimeMatch && !startTime) {
            startTime = startTimeMatch[1].trim();
          }
          
          // Check for End Time
          const endTimeMatch = text.match(/End Time:\s*([\d-]+\s+[\d:]+)/);
          if (endTimeMatch && !endTime) {
            endTime = endTimeMatch[1].trim();
          }
        });
      }
      
      // Extract case counts - improve this to handle more formats
      let passedCases = 0;
      let failedCases = 0;
      let skippedCases = 0;
      
      // First try list items (standard format)
      listItems.forEach(item => {
        const text = item.textContent.trim();
        if (text.startsWith('Case(s) passed:')) {
          passedCases = parseInt(text.replace('Case(s) passed:', '').trim()) || 0;
        } else if (text.startsWith('Cases passed:')) {
          passedCases = parseInt(text.replace('Cases passed:', '').trim()) || 0;
        } else if (text.startsWith('Passed:')) {
          passedCases = parseInt(text.replace('Passed:', '').trim()) || 0;
        } else if (text.startsWith('Case(s) failed:')) {
          failedCases = parseInt(text.replace('Case(s) failed:', '').trim()) || 0;
        } else if (text.startsWith('Cases failed:')) {
          failedCases = parseInt(text.replace('Cases failed:', '').trim()) || 0;
        } else if (text.startsWith('Failed:')) {
          failedCases = parseInt(text.replace('Failed:', '').trim()) || 0;
        } else if (text.startsWith('Case(s) skipped:')) {
          skippedCases = parseInt(text.replace('Case(s) skipped:', '').trim()) || 0;
        } else if (text.startsWith('Cases skipped:')) {
          skippedCases = parseInt(text.replace('Cases skipped:', '').trim()) || 0;
        } else if (text.startsWith('Skipped:')) {
          skippedCases = parseInt(text.replace('Skipped:', '').trim()) || 0;
        }
      });
      
      // If we couldn't find any counts, try examining the test cases table, if present
      if (passedCases === 0 && failedCases === 0 && skippedCases === 0) {
        const table = doc.querySelector('table');
        if (table) {
          // Count passed and failed cases from table rows
          const rows = table.querySelectorAll('tr');
          
          // Skip the header row
          for (let i = 1; i < rows.length; i++) {
            const row = rows[i];
            const statusCell = row.querySelector('td:nth-child(3)');
            if (statusCell) {
              const statusText = statusCell.textContent.trim().toLowerCase();
              if (statusText.includes('pass')) {
                passedCases++;
              } else if (statusText.includes('fail')) {
                failedCases++;
              } else if (statusText.includes('skip') || statusText.includes('n/a')) {
                skippedCases++;
              }
            }
          }
        }
      }
      
      // If still no counts, look for test case counts in the raw HTML
      if (passedCases === 0 && failedCases === 0 && skippedCases === 0) {
        // Look for "Passed: X" or similar patterns
        const passedMatch = html.match(/(?:Cases?|Case\(s\)|Test\(s\))?\s*[Pp]assed\s*:?\s*(\d+)/);
        if (passedMatch) {
          passedCases = parseInt(passedMatch[1]) || 0;
        }
        
        const failedMatch = html.match(/(?:Cases?|Case\(s\)|Test\(s\))?\s*[Ff]ailed\s*:?\s*(\d+)/);
        if (failedMatch) {
          failedCases = parseInt(failedMatch[1]) || 0;
        }
        
        const skippedMatch = html.match(/(?:Cases?|Case\(s\)|Test\(s\))?\s*[Ss]kipped\s*:?\s*(\d+)/);
        if (skippedMatch) {
          skippedCases = parseInt(skippedMatch[1]) || 0;
        }
      }
      
      // If we see individual test cases in the data, count those as a fallback
      if (passedCases === 0 && failedCases === 0 && skippedCases === 0) {
        // Look for PASS Case: or FAIL Case: patterns to count individual test cases
        const passMatches = html.match(/PASS Case:/g);
        if (passMatches) {
          passedCases = passMatches.length;
        }
        
        const failMatches = html.match(/FAIL Case:/g);
        if (failMatches) {
          failedCases = failMatches.length;
        }
      }
      
      // Extract test type and ID
      let testType = 'Test Case';
      let testId = '';
      let testName = '';
      
      // First, look for the name in the report structure (the main UL)
      const ulElements = doc.querySelectorAll('ul');
      if (ulElements.length > 0) {
        const mainUl = ulElements[0];
        const mainLiElements = mainUl.querySelectorAll('li');
        
        for (const li of mainLiElements) {
          const text = li.textContent.trim();
          // Format: FAIL Suite: 322 DEMO PE2.1 Smoke Test
          if (text.includes('Suite:')) {
            testType = 'Test Suite';
            // Extract the name which comes after the ID number
            const match = text.match(/Suite:\s*(\d+)\s+(.*?)(?:\s*(?:PASS|FAIL|Case:|$))/i);
            if (match) {
              testId = match[1];
              testName = match[2].trim();
            }
          }
          // Check individual test cases
          const subLiElements = li.querySelectorAll('li');
          for (const subLi of subLiElements) {
            const subText = subLi.textContent.trim();
            if (subText.includes('Case:')) {
              // Only set these if we haven't found a suite yet
              if (testType !== 'Test Suite') {
                testType = 'Test Case';
                // Extract the name which comes after the ID number
                const match = subText.match(/Case:\s*(\d+)\s+(.*?)(?:\s*(?:PASS|FAIL|Case:|$))/i);
                if (match) {
                  testId = match[1];
                  testName = match[2].trim();
                }
              }
            }
          }
        }
      }

      // If we still don't have the name, extract from more general patterns in the document
      if (!testName || testName.includes('Case:')) {
        // Look for main test suite title
        const testSuiteMatch = html.match(/(?:PASS|FAIL)\s+Suite:\s*(\d+)\s+(.*?)(?:\s*(?:PASS|FAIL|Case:|$))/i);
        if (testSuiteMatch) {
          testType = 'Test Suite';
          testId = testSuiteMatch[1];
          testName = testSuiteMatch[2].trim();
        }
      }
      
      // Clean up the test name to remove any case information
      if (testName) {
        // Remove any "FAIL Case:" or "PASS Case:" segments
        testName = testName.replace(/(?:PASS|FAIL)\s+Case:\s*\d+\s+/g, '');
        // Remove any trailing case references
        testName = testName.split(/\s+(?:PASS|FAIL)\s+Case:/)[0].trim();
      }
      
      // Extract environment - enhance to handle multiple formats
      let environment = 'Unknown';
      
      // First try to find in list items (most common format)
      const variablesSection = Array.from(listItems).find(item => 
        item.textContent.trim().startsWith('Variables:')
      );
      if (variablesSection) {
        const variablesText = variablesSection.textContent.trim();
        const match = variablesText.match(/envir=([^,\s]+)/i);
        if (match) {
          environment = match[1].trim();
        }
      }
      
      // If not found, try to find in the entire HTML
      if (environment === 'Unknown') {
        // Look for Variables:<br/>envir=qa08<br/> pattern
        const htmlMatch = html.match(/Variables:(?:<br\/?>)?envir=([^,\s<]+)/i);
        if (htmlMatch) {
          environment = htmlMatch[1].trim();
        }
      }
      
      // If still not found, try a more general search
      if (environment === 'Unknown') {
        const envMatch = html.match(/envir=([^,\s<&;]+)/i);
        if (envMatch) {
          environment = envMatch[1].trim();
        }
      }
      
      // Calculate duration if both start and end times are available
      let duration = null;
      if (startTime && endTime) {
        try {
          // Parse dates consistently - handle the format 'YYYY-MM-DD HH:MM:SS'
          // First try direct Date parsing
          let start = new Date(startTime);
          let end = new Date(endTime);
          
          // If the dates are invalid, try parsing manually
          if (isNaN(start.getTime()) || isNaN(end.getTime())) {
            // Expected format: "YYYY-MM-DD HH:MM:SS"
            const startMatch = startTime.match(/(\d{4})-(\d{2})-(\d{2})\s+(\d{2}):(\d{2}):(\d{2})/);
            const endMatch = endTime.match(/(\d{4})-(\d{2})-(\d{2})\s+(\d{2}):(\d{2}):(\d{2})/);
            
            if (startMatch && endMatch) {
              // Parse each component (year, month, day, hour, minute, second)
              // Note: months are 0-indexed in JavaScript Date
              start = new Date(
                parseInt(startMatch[1]), // year
                parseInt(startMatch[2]) - 1, // month (0-indexed)
                parseInt(startMatch[3]), // day
                parseInt(startMatch[4]), // hour
                parseInt(startMatch[5]), // minute
                parseInt(startMatch[6])  // second
              );
              
              end = new Date(
                parseInt(endMatch[1]), // year
                parseInt(endMatch[2]) - 1, // month (0-indexed)
                parseInt(endMatch[3]), // day
                parseInt(endMatch[4]), // hour
                parseInt(endMatch[5]), // minute
                parseInt(endMatch[6])  // second
              );
            }
          }
          
          // Calculate duration in milliseconds
          const durationMs = end - start;
          
          // Only proceed if we have valid dates
          if (!isNaN(durationMs) && durationMs >= 0) {
            const durationSec = Math.floor(durationMs / 1000);
            const minutes = Math.floor(durationSec / 60);
            const seconds = durationSec % 60;
            duration = `${minutes}:${seconds.toString().padStart(2, '0')}`;
            
            // For very long durations, include hours
            if (minutes >= 60) {
              const hours = Math.floor(minutes / 60);
              const remainingMinutes = minutes % 60;
              duration = `${hours}:${remainingMinutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
            }
          } else {
            console.warn('Invalid duration calculation:', { startTime, endTime, durationMs });
          }
        } catch (error) {
          console.error('Error calculating duration:', error);
        }
      }
      
      // Build the report data object
      const reportData = {
        tsn_id: tsnId,
        test_id: testId,
        test_name: testName,
        type: testType,
        environment: environment.toUpperCase(),
        status: status === 'PASS' ? 'Success' : (status === 'FAIL' ? 'Failed' : status),
        start_time: startTime,
        end_time: endTime,
        duration: duration,
        total_cases: passedCases + failedCases + skippedCases,
        passed_cases: passedCases,
        failed_cases: failedCases,
        skipped_cases: skippedCases,
        pass_rate: passedCases + failedCases + skippedCases > 0 ? 
          Math.round((passedCases / (passedCases + failedCases + skippedCases)) * 100) : 0
      };
      
      console.log('Parsed report summary data:', reportData);
      return reportData;
    } catch (error) {
      console.error('Error parsing report summary HTML:', error);
      return {
        tsn_id: tsnId,
        status: 'Error',
        error: error.message
      };
    }
  }
  
  /**
   * Parse HTML from ReportDetails endpoint to extract test case details
   * @param {string} html - HTML response from ReportDetails endpoint
   * @param {string} tsnId - Test session ID
   * @returns {Object} - Parsed test case details
   */
  parseReportDetailsHtml(html, tsnId) {
    try {
      // Create a DOM parser
      const parser = new DOMParser();
      const doc = parser.parseFromString(html, 'text/html');
      
      // Extract test cases from the table
      const testCases = [];
      const tableRows = doc.querySelectorAll('table tr');
      
      // Skip the header row
      for (let i = 1; i < tableRows.length; i++) {
        const row = tableRows[i];
        const cells = row.querySelectorAll('td');
        
        if (cells.length >= 5) {
          // Extract data from cells
          const tcId = this.extractTextFromCell(cells[0]);
          const seqIndex = this.extractTextFromCell(cells[1]);
          
          // Determine outcome from the class of the link
          let outcome = 'Unknown';
          const outcomeLink = cells[2].querySelector('a');
          if (outcomeLink) {
            if (outcomeLink.classList.contains('P')) {
              outcome = 'Passed';
            } else if (outcomeLink.classList.contains('F')) {
              outcome = 'Failed';
            }
          }
          
          const description = this.extractTextFromCell(cells[3]);
          const inputOutput = this.extractTextFromCell(cells[4]);
          
          // Extract error message if available
          let errorMessage = '';
          if (outcome === 'Failed') {
            // Try to find error message in the input/output text
            const errorMatch = inputOutput.match(/Error:(.+)/);
            if (errorMatch) {
              errorMessage = errorMatch[1].trim();
            }
          }
          
          // Add test case to the array
          testCases.push({
            tc_id: tcId,
            seq_index: seqIndex,
            status: outcome,
            description: description,
            input_output: inputOutput,
            error_message: errorMessage
          });
        }
      }
      
      // Extract pagination information
      let currentPage = 1;
      let totalPages = 1;
      const paginationText = doc.querySelector('.pagination');
      if (paginationText) {
        const text = paginationText.textContent.trim();
        const match = text.match(/Page (\d+) of (\d+)/);
        if (match) {
          currentPage = parseInt(match[1]) || 1;
          totalPages = parseInt(match[2]) || 1;
        }
      }
      
      // Build the details data object
      const detailsData = {
        tsn_id: tsnId,
        test_cases: testCases,
        pagination: {
          currentPage,
          totalPages
        }
      };
      
      console.log(`Parsed ${testCases.length} test cases from report details`);
      return detailsData;
    } catch (error) {
      console.error('Error parsing report details HTML:', error);
      return {
        tsn_id: tsnId,
        test_cases: [],
        error: error.message
      };
    }
  }
  
  /**
   * Extract text from a table cell
   * @param {Element} cell - Table cell element
   * @returns {string} - Extracted text
   */
  extractTextFromCell(cell) {
    if (!cell) return '';
    
    // Try to get text from a link if present
    const link = cell.querySelector('a');
    if (link) {
      return link.textContent.trim();
    }
    
    // Otherwise get text from the cell
    return cell.textContent.trim();
  }
  
  /**
   * Get recent test runs by fetching multiple report summaries
   * @param {Array<string>} tsnIds - Array of test session IDs
   * @param {string} uid - User ID
   * @param {string} password - Password
   * @param {number} limit - Maximum number of reports to return
   * @returns {Promise<Array>} - Array of report data
   */
  async getRecentTestRuns(tsnIds, uid, password, limit = 10) {
    try {
      console.log(`Getting recent test runs for ${tsnIds.length} session IDs...`);
      
      // Limit the number of session IDs to process
      const limitedIds = tsnIds.slice(0, limit);
      
      // Fetch report summaries for each session ID
      const reportPromises = limitedIds.map(tsnId => 
        this.getReportSummary(tsnId, uid, password)
          .catch(error => {
            console.error(`Error getting report for ${tsnId}:`, error);
            return {
              tsn_id: tsnId,
              status: 'Error',
              error: error.message
            };
          })
      );
      
      // Wait for all promises to resolve
      const reports = await Promise.all(reportPromises);
      
      console.log(`Retrieved ${reports.length} recent test runs`);
      return reports;
    } catch (error) {
      console.error('Error getting recent test runs:', error);
      throw error;
    }
  }
}

// Create global instance
window.externalApiService = new ExternalApiService();
